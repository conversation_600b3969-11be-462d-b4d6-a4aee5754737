"""
RDP Dashboard - De<PERSON> Script

This script demonstrates the system information collection
functionality without starting the full GUI application.

Run this to test if the system information collection is working
properly on your system.

Author: RDP Dashboard Team
"""

import sys
import os
import json
from datetime import datetime

# Add project directories to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logic'))

from logic.system_info import SystemInfoCollector


def print_section(title, data, indent=0):
    """Print a section of data with proper formatting."""
    prefix = "  " * indent
    print(f"{prefix}{'=' * len(title)}")
    print(f"{prefix}{title}")
    print(f"{prefix}{'=' * len(title)}")
    
    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                print(f"{prefix}{key}:")
                print_section("", value, indent + 1)
            else:
                print(f"{prefix}{key}: {value}")
    elif isinstance(data, list):
        for i, item in enumerate(data):
            if isinstance(item, dict):
                print(f"{prefix}Item {i + 1}:")
                print_section("", item, indent + 1)
            else:
                print(f"{prefix}- {item}")
    else:
        print(f"{prefix}{data}")
    
    print()


def main():
    """Main demo function."""
    print("=" * 60)
    print("RDP Dashboard - System Information Demo")
    print("=" * 60)
    print(f"Demo run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Create system info collector
        print("Initializing system information collector...")
        collector = SystemInfoCollector()
        print("✓ System info collector initialized successfully")
        print()
        
        # Collect all system information
        print("Collecting system information...")
        system_info = collector.get_all_system_info()
        print("✓ System information collected successfully")
        print()
        
        # Display the information
        print_section("NETWORK INTERFACES", system_info['network_interfaces'])
        print_section("COMPUTER INFORMATION", system_info['computer_info'])
        print_section("OPERATING SYSTEM", system_info['os_info'])
        print_section("USER INFORMATION", system_info['user_info'])
        print_section("SYSTEM PERFORMANCE", system_info['performance'])
        
        # Save to JSON file for inspection
        output_file = "system_info_demo.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(system_info, f, indent=2, ensure_ascii=False)
        
        print(f"✓ System information saved to: {output_file}")
        print()
        
        print("=" * 60)
        print("Demo completed successfully!")
        print("=" * 60)
        print()
        print("If you can see the system information above, the")
        print("RDP Dashboard application should work correctly.")
        print()
        print("To start the full application, run:")
        print("  python main.py")
        print("or")
        print("  run.bat")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        print()
        print("This indicates there may be an issue with:")
        print("- Missing dependencies")
        print("- System permissions")
        print("- Python environment setup")
        print()
        print("Please check:")
        print("1. All dependencies are installed: pip install -r requirements.txt")
        print("2. You're using Python 3.12")
        print("3. You have necessary system permissions")
        
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
