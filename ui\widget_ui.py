"""
Widget UI Module

This module contains the user interface components and layout
for the RDP Dashboard desktop widget using Plotly Dash.

Author: RDP Dashboard Team
"""

import dash
from dash import html, dcc, Input, Output, callback
import dash_bootstrap_components as dbc
import logging
from typing import Dict, List
from datetime import datetime


class WidgetUI:
    """
    Manages the user interface components for the desktop widget.
    
    This class creates and manages the Dash application layout,
    components, and styling for the system information display.
    """
    
    def __init__(self, system_info_collector):
        """
        Initialize the WidgetUI.
        
        Args:
            system_info_collector: Instance of SystemInfoCollector
        """
        self.logger = logging.getLogger(__name__)
        self.system_info_collector = system_info_collector
        self.app = None
        
        self.logger.info("WidgetUI initialized")
    
    def create_app(self) -> dash.Dash:
        """
        Create and configure the Dash application.
        
        Returns:
            dash.Dash: Configured Dash application instance
        """
        # Create Dash app with Bootstrap theme
        self.app = dash.Dash(
            __name__,
            external_stylesheets=[dbc.themes.BOOTSTRAP],
            suppress_callback_exceptions=True
        )
        
        # Set app title
        self.app.title = "RDP Dashboard Widget"
        
        # Load custom CSS
        try:
            with open('assets/style.css', 'r') as f:
                custom_css = f.read()
            
            # Add custom CSS to app
            self.app.index_string = f'''
            <!DOCTYPE html>
            <html>
                <head>
                    {{%metas%}}
                    <title>{{%title%}}</title>
                    {{%favicon%}}
                    {{%css%}}
                    <style>
                        {custom_css}
                    </style>
                </head>
                <body>
                    {{%app_entry%}}
                    <footer>
                        {{%config%}}
                        {{%scripts%}}
                        {{%renderer%}}
                    </footer>
                </body>
            </html>
            '''
        except Exception as e:
            self.logger.warning(f"Could not load custom CSS: {e}")
        
        # Set up the layout
        self.app.layout = self.create_layout()
        
        # Register callbacks
        self.register_callbacks()
        
        self.logger.info("Dash application created successfully")
        return self.app
    
    def create_layout(self) -> html.Div:
        """
        Create the main layout for the widget.
        
        Returns:
            html.Div: Main layout component
        """
        return html.Div([
            # Auto-refresh interval
            dcc.Interval(
                id='interval-component',
                interval=5000,  # Update every 5 seconds
                n_intervals=0
            ),
            
            # Main widget container
            html.Div([
                # Header
                html.Div([
                    html.H4("RDP Dashboard", className="mb-0"),
                    html.Small("System Information Widget")
                ], className="widget-header"),
                
                # Content area
                html.Div(id="widget-content"),
                
                # Footer with timestamp
                html.Div(id="timestamp-footer", className="timestamp")
                
            ], className="widget-container")
        ])
    
    def create_network_section(self, interfaces: List[Dict]) -> html.Div:
        """
        Create the network interfaces section.
        
        Args:
            interfaces: List of network interface information
            
        Returns:
            html.Div: Network section component
        """
        if not interfaces:
            return html.Div([
                html.Div("Network Interfaces", className="section-title"),
                html.Div("No network interfaces found", className="error-message")
            ], className="info-section")
        
        interface_components = []
        for interface in interfaces:
            interface_components.append(
                html.Div([
                    html.Div(f"{interface['name']} ({interface['type']})", 
                            className="interface-name"),
                    html.Div([
                        html.Div([
                            html.Span("IP Address:", className="info-label"),
                            html.Span(interface['ip_address'], className="info-value")
                        ], className="info-item"),
                        html.Div([
                            html.Span("Netmask:", className="info-label"),
                            html.Span(interface['netmask'], className="info-value")
                        ], className="info-item") if interface['netmask'] != 'N/A' else None
                    ], className="interface-details")
                ], className="network-interface")
            )
        
        return html.Div([
            html.Div("Network Interfaces", className="section-title"),
            html.Div(interface_components)
        ], className="info-section")
    
    def create_computer_section(self, computer_info: Dict) -> html.Div:
        """
        Create the computer information section.
        
        Args:
            computer_info: Computer information dictionary
            
        Returns:
            html.Div: Computer section component
        """
        return html.Div([
            html.Div("Computer Information", className="section-title"),
            html.Div([
                html.Div([
                    html.Span("Computer Name:", className="info-label"),
                    html.Span(computer_info.get('computer_name', 'Unknown'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Domain:", className="info-label"),
                    html.Span(computer_info.get('domain', 'Unknown'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Architecture:", className="info-label"),
                    html.Span(computer_info.get('architecture', 'Unknown'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Boot Time:", className="info-label"),
                    html.Span(computer_info.get('boot_time', 'Unknown'), 
                             className="info-value")
                ], className="info-item")
            ])
        ], className="info-section")
    
    def create_os_section(self, os_info: Dict) -> html.Div:
        """
        Create the operating system section.
        
        Args:
            os_info: Operating system information dictionary
            
        Returns:
            html.Div: OS section component
        """
        return html.Div([
            html.Div("Operating System", className="section-title"),
            html.Div([
                html.Div([
                    html.Span("System:", className="info-label"),
                    html.Span(f"{os_info.get('system', 'Unknown')} {os_info.get('release', '')}", 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Version:", className="info-label"),
                    html.Span(os_info.get('version', 'Unknown')[:30] + "..." 
                             if len(os_info.get('version', '')) > 30 
                             else os_info.get('version', 'Unknown'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Platform:", className="info-label"),
                    html.Span(os_info.get('platform', 'Unknown')[:25] + "..." 
                             if len(os_info.get('platform', '')) > 25 
                             else os_info.get('platform', 'Unknown'), 
                             className="info-value")
                ], className="info-item")
            ])
        ], className="info-section")
    
    def create_user_section(self, user_info: Dict) -> html.Div:
        """
        Create the user information section.
        
        Args:
            user_info: User information dictionary
            
        Returns:
            html.Div: User section component
        """
        return html.Div([
            html.Div("User Information", className="section-title"),
            html.Div([
                html.Div([
                    html.Span("Username:", className="info-label"),
                    html.Span(user_info.get('username', 'Unknown'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Session Start:", className="info-label"),
                    html.Span(user_info.get('session_start', 'Unknown'), 
                             className="info-value")
                ], className="info-item")
            ])
        ], className="info-section")
    
    def create_performance_section(self, performance: Dict) -> html.Div:
        """
        Create the system performance section.
        
        Args:
            performance: Performance metrics dictionary
            
        Returns:
            html.Div: Performance section component
        """
        return html.Div([
            html.Div("System Performance", className="section-title"),
            html.Div([
                html.Div([
                    html.Span("CPU Usage:", className="info-label"),
                    html.Span(performance.get('cpu_usage', 'N/A'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Memory:", className="info-label"),
                    html.Span(performance.get('memory_usage', 'N/A'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Memory Details:", className="info-label"),
                    html.Span(performance.get('memory_details', 'N/A'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Disk Usage:", className="info-label"),
                    html.Span(performance.get('disk_usage', 'N/A'), 
                             className="info-value")
                ], className="info-item"),
                html.Div([
                    html.Span("Disk Details:", className="info-label"),
                    html.Span(performance.get('disk_details', 'N/A'), 
                             className="info-value")
                ], className="info-item")
            ])
        ], className="info-section")
    
    def register_callbacks(self):
        """Register Dash callbacks for interactivity."""
        
        @self.app.callback(
            [Output('widget-content', 'children'),
             Output('timestamp-footer', 'children')],
            [Input('interval-component', 'n_intervals')]
        )
        def update_widget_content(n):
            """Update the widget content with fresh system information."""
            try:
                # Get fresh system information
                system_info = self.system_info_collector.get_all_system_info()
                
                # Create content sections
                content = [
                    self.create_network_section(system_info['network_interfaces']),
                    self.create_computer_section(system_info['computer_info']),
                    self.create_os_section(system_info['os_info']),
                    self.create_user_section(system_info['user_info']),
                    self.create_performance_section(system_info['performance'])
                ]
                
                # Create timestamp
                timestamp = f"Last updated: {system_info['timestamp']}"
                
                return content, timestamp
                
            except Exception as e:
                self.logger.error(f"Error updating widget content: {e}")
                error_content = html.Div([
                    html.Div("Error", className="section-title"),
                    html.Div(f"Failed to load system information: {str(e)}", 
                            className="error-message")
                ], className="info-section")
                
                return [error_content], f"Error at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
