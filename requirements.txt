# RDP Dashboard - Desktop Widget Requirements
# 
# To create conda environment with Python 3.12:
# conda create -n rdp-dashboard python=3.12
# conda activate rdp-dashboard
# pip install -r requirements.txt

# Core GUI Framework
dash==2.17.1
dash-bootstrap-components==1.5.0

# System Information
psutil==5.9.6
# Note: netifaces might need compilation on some systems
# Alternative: use psutil.net_if_addrs() if netifaces fails
netifaces==0.11.0

# Threading support is built into Python standard library

# Windows-specific features
pywin32==306
pystray==0.19.5
Pillow==10.1.0

# Logging and Configuration
colorlog==6.8.0

# Testing (Optional)
pytest==7.4.3
pytest-cov==4.1.0

# Development Tools
black==23.11.0
flake8==6.1.0
