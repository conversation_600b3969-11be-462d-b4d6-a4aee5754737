"""
Standalone Floating Widget Script
Generated by RDP Dashboard
"""

import webview
import sys
import time

def main():
    """Main function for the floating widget."""
    try:
        # Wait a moment for the server to be ready
        time.sleep(1)
        
        # Create the floating window
        window = webview.create_window(
            title="RDP Dashboard",
            url="http://127.0.0.1:8050",
            width=320,
            height=650,
            min_size=(280, 400),
            resizable=True,
            on_top=True,
        )
        
        # Start the webview
        webview.start(debug=False)
        
    except Exception as e:
        print(f"Error in floating widget: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
