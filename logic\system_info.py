"""
System Information Module

This module provides functions to gather system information including:
- Network interfaces and IP addresses
- Computer name and operating system details
- Current logged-in user information
- System performance metrics

Author: RDP Dashboard Team
"""

import socket
import platform
import getpass
import psutil
import logging
from typing import Dict, List
from datetime import datetime

# Try to import netifaces, fall back to psutil if not available
try:
    import netifaces
    HAS_NETIFACES = True
except ImportError:
    HAS_NETIFACES = False
    print("Warning: netifaces not available, using psutil for network info")


class SystemInfoCollector:
    """
    Collects and manages system information for the desktop widget.
    
    This class provides methods to gather various system metrics and
    information that will be displayed in the desktop overlay widget.
    """
    
    def __init__(self):
        """Initialize the SystemInfoCollector."""
        self.logger = logging.getLogger(__name__)
        self.logger.info("SystemInfoCollector initialized")
    
    def get_network_interfaces(self) -> List[Dict[str, str]]:
        """
        Get all network interfaces with their IP addresses.

        Returns:
            List[Dict[str, str]]: List of network interfaces with details
        """
        interfaces = []

        try:
            if HAS_NETIFACES:
                interfaces = self._get_interfaces_netifaces()
            else:
                interfaces = self._get_interfaces_psutil()

        except Exception as e:
            self.logger.error(f"Error getting network interfaces: {e}")
            interfaces.append({
                'name': 'Error',
                'ip_address': 'Unable to retrieve',
                'netmask': 'N/A',
                'type': 'Error'
            })

        return interfaces

    def _get_interfaces_netifaces(self) -> List[Dict[str, str]]:
        """Get network interfaces using netifaces library."""
        interfaces = []

        try:
            # Get all network interfaces
            for interface_name in netifaces.interfaces():
                interface_info = netifaces.ifaddresses(interface_name)
                
                # Skip loopback and inactive interfaces
                if interface_name.startswith('Loopback'):
                    continue
                
                # Get IPv4 addresses
                if netifaces.AF_INET in interface_info:
                    for addr_info in interface_info[netifaces.AF_INET]:
                        ip_address = addr_info.get('addr', 'N/A')
                        netmask = addr_info.get('netmask', 'N/A')
                        
                        if ip_address and ip_address != '127.0.0.1':
                            interfaces.append({
                                'name': interface_name,
                                'ip_address': ip_address,
                                'netmask': netmask,
                                'type': 'IPv4'
                            })
                
                # Get IPv6 addresses
                if netifaces.AF_INET6 in interface_info:
                    for addr_info in interface_info[netifaces.AF_INET6]:
                        ip_address = addr_info.get('addr', 'N/A')
                        
                        if ip_address and not ip_address.startswith('::1'):
                            # Clean up IPv6 address (remove zone identifier)
                            ip_address = ip_address.split('%')[0]
                            interfaces.append({
                                'name': interface_name,
                                'ip_address': ip_address,
                                'netmask': 'N/A',
                                'type': 'IPv6'
                            })
        except Exception as e:
            self.logger.error(f"Error with netifaces: {e}")
            raise

        return interfaces

    def _get_interfaces_psutil(self) -> List[Dict[str, str]]:
        """Get network interfaces using psutil library as fallback."""
        interfaces = []

        try:
            # Get network interfaces using psutil
            net_if_addrs = psutil.net_if_addrs()

            for interface_name, addr_list in net_if_addrs.items():
                # Skip loopback interfaces
                if 'loopback' in interface_name.lower() or interface_name.startswith('lo'):
                    continue

                for addr in addr_list:
                    if addr.family == socket.AF_INET:  # IPv4
                        interfaces.append({
                            'name': interface_name,
                            'ip_address': addr.address,
                            'netmask': addr.netmask or 'N/A',
                            'type': 'IPv4'
                        })
                    elif addr.family == socket.AF_INET6:  # IPv6
                        # Skip link-local IPv6 addresses
                        if not addr.address.startswith('fe80'):
                            interfaces.append({
                                'name': interface_name,
                                'ip_address': addr.address.split('%')[0],  # Remove zone ID
                                'netmask': 'N/A',
                                'type': 'IPv6'
                            })
        except Exception as e:
            self.logger.error(f"Error with psutil network interfaces: {e}")
            raise

        return interfaces
    
    def get_computer_info(self) -> Dict[str, str]:
        """
        Get computer name and basic system information.
        
        Returns:
            Dict[str, str]: Computer information dictionary
        """
        try:
            computer_info = {
                'computer_name': socket.gethostname(),
                'domain': socket.getfqdn(),
                'architecture': platform.architecture()[0],
                'processor': platform.processor() or platform.machine(),
                'boot_time': datetime.fromtimestamp(
                    psutil.boot_time()
                ).strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            self.logger.error(f"Error getting computer info: {e}")
            computer_info = {
                'computer_name': 'Unknown',
                'domain': 'Unknown',
                'architecture': 'Unknown',
                'processor': 'Unknown',
                'boot_time': 'Unknown'
            }
        
        return computer_info
    
    def get_operating_system_info(self) -> Dict[str, str]:
        """
        Get detailed operating system information.
        
        Returns:
            Dict[str, str]: Operating system information dictionary
        """
        try:
            os_info = {
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'platform': platform.platform(),
                'python_version': platform.python_version()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting OS info: {e}")
            os_info = {
                'system': 'Unknown',
                'release': 'Unknown',
                'version': 'Unknown',
                'platform': 'Unknown',
                'python_version': 'Unknown'
            }
        
        return os_info
    
    def get_user_info(self) -> Dict[str, str]:
        """
        Get current user information.
        
        Returns:
            Dict[str, str]: User information dictionary
        """
        try:
            user_info = {
                'username': getpass.getuser(),
                'home_directory': psutil.users()[0].name if psutil.users() else 'Unknown',
                'session_start': datetime.fromtimestamp(
                    psutil.users()[0].started
                ).strftime('%Y-%m-%d %H:%M:%S') if psutil.users() else 'Unknown'
            }
            
        except Exception as e:
            self.logger.error(f"Error getting user info: {e}")
            user_info = {
                'username': getpass.getuser(),
                'home_directory': 'Unknown',
                'session_start': 'Unknown'
            }
        
        return user_info
    
    def get_system_performance(self) -> Dict[str, str]:
        """
        Get basic system performance metrics.
        
        Returns:
            Dict[str, str]: System performance metrics
        """
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used = round(memory.used / (1024**3), 2)  # GB
            memory_total = round(memory.total / (1024**3), 2)  # GB
            
            # Get disk usage for C: drive
            disk = psutil.disk_usage('C:')
            disk_percent = (disk.used / disk.total) * 100
            disk_free = round(disk.free / (1024**3), 2)  # GB
            disk_total = round(disk.total / (1024**3), 2)  # GB
            
            performance_info = {
                'cpu_usage': f"{cpu_percent}%",
                'memory_usage': f"{memory_percent}%",
                'memory_details': f"{memory_used}GB / {memory_total}GB",
                'disk_usage': f"{disk_percent:.1f}%",
                'disk_details': f"{disk_free}GB free / {disk_total}GB total"
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance info: {e}")
            performance_info = {
                'cpu_usage': 'N/A',
                'memory_usage': 'N/A',
                'memory_details': 'N/A',
                'disk_usage': 'N/A',
                'disk_details': 'N/A'
            }
        
        return performance_info
    
    def get_all_system_info(self) -> Dict:
        """
        Get all system information in a single call.
        
        Returns:
            Dict: Complete system information dictionary
        """
        self.logger.info("Collecting all system information")
        
        return {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'network_interfaces': self.get_network_interfaces(),
            'computer_info': self.get_computer_info(),
            'os_info': self.get_operating_system_info(),
            'user_info': self.get_user_info(),
            'performance': self.get_system_performance()
        }
