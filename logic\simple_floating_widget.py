"""
Simple Floating Widget Module

A simplified floating widget implementation that works better
with the current application architecture.

Author: RDP Dashboard Team
"""

import subprocess
import sys
import os
import logging
import json
from typing import Optional


class SimpleFloatingWidget:
    """
    A simplified floating widget that launches the webview in a separate process.
    
    This approach avoids threading issues by running the floating widget
    as a separate Python process.
    """
    
    def __init__(self, app_instance=None, widget_url="http://127.0.0.1:8050"):
        """
        Initialize the SimpleFloatingWidget.
        
        Args:
            app_instance: Reference to the main application instance
            widget_url: URL of the Dash application
        """
        self.logger = logging.getLogger(__name__)
        self.app_instance = app_instance
        self.widget_url = widget_url
        self.process = None
        self.is_running = False
        
        # Widget configuration
        self.widget_width = 320
        self.widget_height = 650
        
        self.logger.info("SimpleFloatingWidget initialized")
    
    def create_floating_widget_script(self) -> str:
        """
        Create a standalone script for the floating widget.
        
        Returns:
            str: Path to the created script
        """
        script_content = f'''"""
Standalone Floating Widget Script
Generated by RDP Dashboard
"""

import webview
import sys
import time

def main():
    """Main function for the floating widget."""
    try:
        # Wait a moment for the server to be ready
        time.sleep(1)
        
        # Create the floating window
        window = webview.create_window(
            title="RDP Dashboard",
            url="{self.widget_url}",
            width={self.widget_width},
            height={self.widget_height},
            min_size=(280, 400),
            resizable=True,
            on_top=True,
        )
        
        # Start the webview
        webview.start(debug=False)
        
    except Exception as e:
        print(f"Error in floating widget: {{e}}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
        
        script_path = "floating_widget_standalone.py"
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self.logger.info(f"Created floating widget script: {script_path}")
            return script_path
            
        except Exception as e:
            self.logger.error(f"Error creating floating widget script: {e}")
            raise
    
    def start_floating_widget(self):
        """Start the floating widget in a separate process."""
        try:
            self.logger.info("Starting floating widget in separate process...")
            
            # Create the standalone script
            script_path = self.create_floating_widget_script()
            
            # Get the current Python executable
            python_exe = sys.executable
            
            # Start the floating widget process
            self.process = subprocess.Popen(
                [python_exe, script_path],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            self.is_running = True
            self.logger.info(f"Floating widget started with PID: {self.process.pid}")
            
        except Exception as e:
            self.logger.error(f"Error starting floating widget: {e}")
            raise
    
    def stop_floating_widget(self):
        """Stop the floating widget process."""
        try:
            if self.process and self.is_running:
                self.logger.info("Stopping floating widget...")
                self.process.terminate()
                
                # Wait for process to terminate
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.logger.warning("Floating widget process didn't terminate, killing...")
                    self.process.kill()
                
                self.process = None
                self.is_running = False
                self.logger.info("Floating widget stopped")
                
        except Exception as e:
            self.logger.error(f"Error stopping floating widget: {e}")
    
    def is_widget_running(self) -> bool:
        """
        Check if the floating widget is running.
        
        Returns:
            bool: True if running, False otherwise
        """
        if not self.process:
            return False
        
        # Check if process is still alive
        poll_result = self.process.poll()
        if poll_result is not None:
            # Process has terminated
            self.is_running = False
            self.process = None
            return False
        
        return self.is_running
    
    def restart_floating_widget(self):
        """Restart the floating widget."""
        try:
            self.logger.info("Restarting floating widget...")
            self.stop_floating_widget()
            time.sleep(1)  # Brief pause
            self.start_floating_widget()
            
        except Exception as e:
            self.logger.error(f"Error restarting floating widget: {e}")
    
    def show_widget(self):
        """Show the floating widget (start if not running)."""
        try:
            if not self.is_widget_running():
                self.start_floating_widget()
            else:
                self.logger.info("Floating widget is already running")
                
        except Exception as e:
            self.logger.error(f"Error showing floating widget: {e}")
    
    def hide_widget(self):
        """Hide the floating widget (stop the process)."""
        try:
            self.stop_floating_widget()
            
        except Exception as e:
            self.logger.error(f"Error hiding floating widget: {e}")
    
    def toggle_widget(self):
        """Toggle the floating widget visibility."""
        try:
            if self.is_widget_running():
                self.hide_widget()
            else:
                self.show_widget()
                
        except Exception as e:
            self.logger.error(f"Error toggling floating widget: {e}")
    
    def get_widget_info(self) -> dict:
        """
        Get information about the floating widget.
        
        Returns:
            dict: Widget information
        """
        return {
            'is_running': self.is_widget_running(),
            'width': self.widget_width,
            'height': self.widget_height,
            'url': self.widget_url,
            'pid': self.process.pid if self.process else None
        }
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.stop_floating_widget()
            
            # Remove the standalone script
            script_path = "floating_widget_standalone.py"
            if os.path.exists(script_path):
                os.remove(script_path)
                self.logger.info("Cleaned up floating widget script")
                
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# Import time for the restart method
import time
