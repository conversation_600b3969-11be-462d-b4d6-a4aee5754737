# RDP Dashboard - Troubleshooting Guide

This guide helps resolve common installation and runtime issues with the RDP Dashboard application.

## Installation Issues

### Issue: `netifaces==0.11.0` installation fails

**Error Message:**
```
ERROR: Could not find a version that satisfies the requirement threading-timer==0.1.0
ERROR: No matching distribution found for threading-timer==0.1.0
```

**Solution 1: Use Minimal Installation**
```bash
pip install -r requirements-minimal.txt
```

**Solution 2: Install netifaces separately**
```bash
# Try installing netifaces with conda instead of pip
conda install -c conda-forge netifaces

# Or install without netifaces (app will use psutil fallback)
pip install dash dash-bootstrap-components psutil pywin32 pystray Pillow colorlog
```

**Solution 3: Manual Installation**
```bash
# Install packages one by one
pip install dash==2.17.1
pip install dash-bootstrap-components==1.5.0
pip install psutil==5.9.6
pip install pywin32==306
pip install pystray==0.19.5
pip install Pillow==10.1.0
pip install colorlog==6.8.0
```

### Issue: `pywin32` installation fails

**Solution:**
```bash
# Use conda to install pywin32
conda install pywin32

# Or use pip with --force-reinstall
pip install --force-reinstall pywin32
```

### Issue: `pystray` installation fails

**Solution:**
```bash
# Install dependencies first
pip install Pillow
pip install pystray

# Or use conda
conda install -c conda-forge pystray
```

## Runtime Issues

### Issue: Application won't start

**Check 1: Environment Activation**
```bash
conda activate rdp-dashboard
python --version  # Should show Python 3.12.x
```

**Check 2: Test Installation**
```bash
python test_installation.py
```

**Check 3: Run Demo**
```bash
python demo.py
```

### Issue: Widget not displaying

**Check 1: Server Running**
- Look for "Dash is running on http://127.0.0.1:8050/" message
- Open http://127.0.0.1:8050 in browser manually

**Check 2: Port Conflict**
```bash
# Check if port 8050 is in use
netstat -an | findstr :8050
```

**Solution: Change Port**
Edit `config.py` and change `SERVER_PORT = 8050` to another port like `8051`.

### Issue: System tray icon missing

**Check 1: Windows Notification Settings**
- Right-click taskbar → Taskbar settings
- Turn on "Always show all icons in the notification area"

**Check 2: Run as Administrator**
- Right-click Command Prompt → "Run as administrator"
- Navigate to project folder and run application

### Issue: Network information not showing

**This is expected if netifaces failed to install.**

The application automatically falls back to using `psutil` for network information. You should still see network interfaces, but the format might be slightly different.

### Issue: High CPU usage

**Solution 1: Increase Refresh Interval**
Edit `config.py`:
```python
REFRESH_INTERVAL = 10000  # Change from 5000 to 10000 (10 seconds)
```

**Solution 2: Disable Performance Monitoring**
Edit `config.py`:
```python
ENABLE_PERFORMANCE_MONITORING = False
```

## Testing and Debugging

### Run Installation Test
```bash
python test_installation.py
```

### Run System Info Demo
```bash
python demo.py
```

### Run Unit Tests
```bash
python -m pytest tests/ -v
# or
python tests/test_system_info.py
```

### Enable Debug Logging
Edit `config.py`:
```python
LOG_LEVEL = "DEBUG"
```

Then check the log file: `logs/rdp_dashboard.log`

## Common Error Messages

### "ModuleNotFoundError: No module named 'netifaces'"
**Solution:** This is expected if netifaces installation failed. The app will use psutil fallback automatically.

### "Permission denied" errors
**Solution:** Run Command Prompt as Administrator or check Windows Defender/antivirus settings.

### "Port already in use"
**Solution:** Change the port in `config.py` or kill the process using the port:
```bash
netstat -ano | findstr :8050
taskkill /PID <process_id> /F
```

### "Failed to create conda environment"
**Solution:** 
1. Make sure Anaconda/Miniconda is installed
2. Update conda: `conda update conda`
3. Try creating environment manually:
   ```bash
   conda create -n rdp-dashboard python=3.12 -y
   conda activate rdp-dashboard
   ```

## Alternative Installation Methods

### Method 1: Without Conda
```bash
# Use system Python (requires Python 3.12)
python -m venv rdp-dashboard
rdp-dashboard\Scripts\activate
pip install -r requirements-minimal.txt
```

### Method 2: Docker (Advanced)
```dockerfile
FROM python:3.12-slim
WORKDIR /app
COPY requirements-minimal.txt .
RUN pip install -r requirements-minimal.txt
COPY . .
CMD ["python", "main.py"]
```

## Getting Help

1. **Check Log Files:** `logs/rdp_dashboard.log`
2. **Run Diagnostic Tools:** Use `test_installation.py` and `demo.py`
3. **Try Minimal Installation:** Use `requirements-minimal.txt`
4. **Check System Requirements:** Windows 10/11, Python 3.12

## Known Limitations

1. **netifaces Dependency:** May fail on some systems due to compilation requirements
2. **Windows Only:** Designed specifically for Windows (system tray, pywin32)
3. **Port Conflicts:** Default port 8050 may conflict with other applications
4. **Antivirus Software:** May flag the application due to system information access

## Success Indicators

✅ **Installation Successful:**
- `test_installation.py` shows all required modules OK
- `demo.py` displays system information
- No error messages in console

✅ **Application Running:**
- System tray icon appears
- Widget accessible at http://127.0.0.1:8050
- Log file shows "Application started successfully"

If you continue to experience issues, try the minimal installation approach or run the diagnostic scripts for more specific error information.
