"""
Unit Tests for System Information Module

This module contains unit tests for the SystemInfoCollector class
to ensure proper functionality of system information gathering.

Author: RDP Dashboard Team
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logic.system_info import SystemInfoCollector


class TestSystemInfoCollector(unittest.TestCase):
    """Test cases for SystemInfoCollector class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.collector = SystemInfoCollector()
    
    def test_initialization(self):
        """Test that SystemInfoCollector initializes correctly."""
        self.assertIsInstance(self.collector, SystemInfoCollector)
        self.assertIsNotNone(self.collector.logger)
    
    @patch('logic.system_info.netifaces.interfaces')
    @patch('logic.system_info.netifaces.ifaddresses')
    def test_get_network_interfaces_success(self, mock_ifaddresses, mock_interfaces):
        """Test successful network interface retrieval."""
        # Mock network interfaces
        mock_interfaces.return_value = ['Ethernet', 'Wi-Fi']
        mock_ifaddresses.side_effect = [
            {
                2: [{'addr': '*************', 'netmask': '*************'}]  # AF_INET = 2
            },
            {
                2: [{'addr': '*********', 'netmask': '*************'}]
            }
        ]
        
        interfaces = self.collector.get_network_interfaces()
        
        self.assertIsInstance(interfaces, list)
        self.assertGreaterEqual(len(interfaces), 0)
        
        if interfaces:
            interface = interfaces[0]
            self.assertIn('name', interface)
            self.assertIn('ip_address', interface)
            self.assertIn('netmask', interface)
            self.assertIn('type', interface)
    
    @patch('logic.system_info.netifaces.interfaces')
    def test_get_network_interfaces_error(self, mock_interfaces):
        """Test network interface retrieval with error."""
        mock_interfaces.side_effect = Exception("Network error")
        
        interfaces = self.collector.get_network_interfaces()
        
        self.assertIsInstance(interfaces, list)
        self.assertEqual(len(interfaces), 1)
        self.assertEqual(interfaces[0]['name'], 'Error')
        self.assertEqual(interfaces[0]['ip_address'], 'Unable to retrieve')
    
    @patch('logic.system_info.socket.gethostname')
    @patch('logic.system_info.socket.getfqdn')
    @patch('logic.system_info.platform.architecture')
    @patch('logic.system_info.platform.processor')
    @patch('logic.system_info.psutil.boot_time')
    def test_get_computer_info_success(self, mock_boot_time, mock_processor, 
                                     mock_architecture, mock_getfqdn, mock_gethostname):
        """Test successful computer information retrieval."""
        # Mock return values
        mock_gethostname.return_value = 'TEST-PC'
        mock_getfqdn.return_value = 'test-pc.domain.com'
        mock_architecture.return_value = ('64bit', 'WindowsPE')
        mock_processor.return_value = 'Intel64 Family 6 Model 142 Stepping 10, GenuineIntel'
        mock_boot_time.return_value = 1640995200.0  # 2022-01-01 00:00:00
        
        computer_info = self.collector.get_computer_info()
        
        self.assertIsInstance(computer_info, dict)
        self.assertEqual(computer_info['computer_name'], 'TEST-PC')
        self.assertEqual(computer_info['domain'], 'test-pc.domain.com')
        self.assertEqual(computer_info['architecture'], '64bit')
        self.assertIn('processor', computer_info)
        self.assertIn('boot_time', computer_info)
    
    @patch('logic.system_info.socket.gethostname')
    def test_get_computer_info_error(self, mock_gethostname):
        """Test computer information retrieval with error."""
        mock_gethostname.side_effect = Exception("Socket error")
        
        computer_info = self.collector.get_computer_info()
        
        self.assertIsInstance(computer_info, dict)
        self.assertEqual(computer_info['computer_name'], 'Unknown')
        self.assertEqual(computer_info['domain'], 'Unknown')
    
    @patch('logic.system_info.platform.system')
    @patch('logic.system_info.platform.release')
    @patch('logic.system_info.platform.version')
    @patch('logic.system_info.platform.platform')
    @patch('logic.system_info.platform.python_version')
    def test_get_operating_system_info_success(self, mock_python_version, mock_platform,
                                             mock_version, mock_release, mock_system):
        """Test successful operating system information retrieval."""
        # Mock return values
        mock_system.return_value = 'Windows'
        mock_release.return_value = '10'
        mock_version.return_value = '10.0.19041'
        mock_platform.return_value = 'Windows-10-10.0.19041-SP0'
        mock_python_version.return_value = '3.12.0'
        
        os_info = self.collector.get_operating_system_info()
        
        self.assertIsInstance(os_info, dict)
        self.assertEqual(os_info['system'], 'Windows')
        self.assertEqual(os_info['release'], '10')
        self.assertEqual(os_info['version'], '10.0.19041')
        self.assertEqual(os_info['python_version'], '3.12.0')
    
    @patch('logic.system_info.getpass.getuser')
    @patch('logic.system_info.psutil.users')
    def test_get_user_info_success(self, mock_users, mock_getuser):
        """Test successful user information retrieval."""
        # Mock return values
        mock_getuser.return_value = 'testuser'
        mock_user = MagicMock()
        mock_user.name = 'testuser'
        mock_user.started = 1640995200.0  # 2022-01-01 00:00:00
        mock_users.return_value = [mock_user]
        
        user_info = self.collector.get_user_info()
        
        self.assertIsInstance(user_info, dict)
        self.assertEqual(user_info['username'], 'testuser')
        self.assertEqual(user_info['home_directory'], 'testuser')
        self.assertIn('session_start', user_info)
    
    @patch('logic.system_info.psutil.cpu_percent')
    @patch('logic.system_info.psutil.virtual_memory')
    @patch('logic.system_info.psutil.disk_usage')
    def test_get_system_performance_success(self, mock_disk_usage, mock_virtual_memory, mock_cpu_percent):
        """Test successful system performance retrieval."""
        # Mock return values
        mock_cpu_percent.return_value = 25.5
        
        mock_memory = MagicMock()
        mock_memory.percent = 60.0
        mock_memory.used = 8 * 1024**3  # 8 GB
        mock_memory.total = 16 * 1024**3  # 16 GB
        mock_virtual_memory.return_value = mock_memory
        
        mock_disk = MagicMock()
        mock_disk.used = 500 * 1024**3  # 500 GB
        mock_disk.free = 500 * 1024**3  # 500 GB
        mock_disk.total = 1000 * 1024**3  # 1000 GB
        mock_disk_usage.return_value = mock_disk
        
        performance = self.collector.get_system_performance()
        
        self.assertIsInstance(performance, dict)
        self.assertEqual(performance['cpu_usage'], '25.5%')
        self.assertEqual(performance['memory_usage'], '60.0%')
        self.assertIn('memory_details', performance)
        self.assertIn('disk_usage', performance)
        self.assertIn('disk_details', performance)
    
    def test_get_all_system_info(self):
        """Test that get_all_system_info returns complete information."""
        system_info = self.collector.get_all_system_info()
        
        self.assertIsInstance(system_info, dict)
        self.assertIn('timestamp', system_info)
        self.assertIn('network_interfaces', system_info)
        self.assertIn('computer_info', system_info)
        self.assertIn('os_info', system_info)
        self.assertIn('user_info', system_info)
        self.assertIn('performance', system_info)
        
        # Check that each section is the correct type
        self.assertIsInstance(system_info['network_interfaces'], list)
        self.assertIsInstance(system_info['computer_info'], dict)
        self.assertIsInstance(system_info['os_info'], dict)
        self.assertIsInstance(system_info['user_info'], dict)
        self.assertIsInstance(system_info['performance'], dict)


class TestSystemInfoIntegration(unittest.TestCase):
    """Integration tests for system information collection."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.collector = SystemInfoCollector()
    
    def test_real_system_info_collection(self):
        """Test actual system information collection (integration test)."""
        # This test runs against the real system
        # It should not fail unless there's a serious system issue
        
        try:
            system_info = self.collector.get_all_system_info()
            
            # Basic structure checks
            self.assertIsInstance(system_info, dict)
            self.assertIn('timestamp', system_info)
            
            # Network interfaces should be a list
            self.assertIsInstance(system_info['network_interfaces'], list)
            
            # Computer info should have basic fields
            computer_info = system_info['computer_info']
            self.assertIn('computer_name', computer_info)
            self.assertNotEqual(computer_info['computer_name'], 'Unknown')
            
            # OS info should have system type
            os_info = system_info['os_info']
            self.assertIn('system', os_info)
            self.assertNotEqual(os_info['system'], 'Unknown')
            
            # User info should have username
            user_info = system_info['user_info']
            self.assertIn('username', user_info)
            self.assertNotEqual(user_info['username'], 'Unknown')
            
        except Exception as e:
            self.fail(f"Real system info collection failed: {e}")


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestSystemInfoCollector))
    test_suite.addTest(unittest.makeSuite(TestSystemInfoIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
