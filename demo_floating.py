"""
Floating Widget Demo

This script demonstrates the floating widget functionality
by creating a simple floating window that displays the
RDP Dashboard content.

Author: RDP Dashboard Team
"""

import webview
import time
import threading
import webbrowser


def create_floating_demo():
    """Create a simple floating widget demo."""
    print("=" * 50)
    print("RDP Dashboard - Floating Widget Demo")
    print("=" * 50)
    print()
    
    # Check if Dash server is running
    print("Checking if Dash server is running...")
    try:
        import requests
        response = requests.get("http://127.0.0.1:8050", timeout=5)
        if response.status_code == 200:
            print("✓ Dash server is running")
        else:
            print("❌ Dash server returned error")
            return False
    except Exception as e:
        print(f"❌ Dash server not accessible: {e}")
        print()
        print("Please start the main application first:")
        print("  python main.py")
        print()
        print("Then run this demo in another terminal.")
        return False
    
    print()
    print("Creating floating widget window...")
    
    try:
        # Create a floating window
        window = webview.create_window(
            title="RDP Dashboard - Floating Widget",
            url="http://127.0.0.1:8050",
            width=320,
            height=650,
            min_size=(280, 400),
            resizable=True,
            on_top=True,
        )
        
        print("✓ Floating widget window created")
        print()
        print("Starting floating widget...")
        print("Note: The floating widget should appear on your screen.")
        print("You can drag it around and resize it.")
        print()
        print("Press Ctrl+C to close the floating widget.")
        
        # Start the webview
        webview.start(debug=False)
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating floating widget: {e}")
        return False


def main():
    """Main demo function."""
    try:
        success = create_floating_demo()
        if success:
            print()
            print("Floating widget demo completed successfully!")
        else:
            print()
            print("Floating widget demo failed.")
            return 1
            
    except KeyboardInterrupt:
        print()
        print("Demo interrupted by user.")
        return 0
    except Exception as e:
        print(f"Demo error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
