2025-06-10 15:19:53 - __main__ - INFO - RDP Dashboard Application initialized
2025-06-10 15:19:53 - __main__ - INFO - Starting RDP Dashboard Application...
2025-06-10 15:19:53 - __main__ - INFO - Initializing application components...
2025-06-10 15:19:53 - logic.system_info - INFO - SystemInfoCollector initialized
2025-06-10 15:19:53 - __main__ - INFO - System info collector initialized
2025-06-10 15:19:53 - logic.window_manager - INFO - WindowManager initialized
2025-06-10 15:19:53 - __main__ - INFO - Window manager initialized
2025-06-10 15:19:53 - ui.widget_ui - INFO - WidgetUI initialized
2025-06-10 15:19:53 - ui.widget_ui - INFO - Dash application created successfully
2025-06-10 15:19:53 - __main__ - INFO - Widget UI initialized
2025-06-10 15:19:53 - __main__ - INFO - All components initialized successfully
2025-06-10 15:19:53 - __main__ - INFO - Starting Dash server...
2025-06-10 15:19:53 - __main__ - INFO - Dash server thread started
2025-06-10 15:19:53 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-06-10 15:19:53 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8050
2025-06-10 15:19:53 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 15:19:55 - logic.window_manager - INFO - System tray icon created successfully
2025-06-10 15:19:55 - logic.window_manager - INFO - Starting system tray
2025-06-10 15:19:55 - logic.window_manager - INFO - System tray started in background thread
2025-06-10 15:19:55 - __main__ - INFO - RDP Dashboard Application started successfully
2025-06-10 15:19:55 - __main__ - INFO - Widget available at: http://127.0.0.1:8050
2025-06-10 15:19:55 - __main__ - INFO - Check system tray for options
2025-06-10 15:20:27 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:27] "GET / HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET / HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/react@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/prop-types@15.v2_17_1m1749539957.8.1.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dash-renderer/build/dash_renderer.v2_17_1m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash_bootstrap_components/_components/dash_bootstrap_components.v1_5_0m1749539958.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dcc/dash_core_components-shared.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dash_table/bundle.v5_2_11m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/react-dom@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/polyfill@7.v2_17_1m1749539957.12.1.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dcc/dash_core_components.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/html/dash_html_components.v2_0_18m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-layout HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_favicon.ico?v=2.17.1 HTTP/1.1" 200 -
2025-06-10 15:20:34 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:35 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:39 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:40 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:44 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:45 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:45] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:49 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:50 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:50] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:54 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:55 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:55] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:59 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:21:00 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:21:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:21:04 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:21:05 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:21:05] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:22:43 - __main__ - INFO - Received signal 2, shutting down...
2025-06-10 15:22:43 - __main__ - INFO - Shutting down RDP Dashboard Application...
2025-06-10 15:22:43 - logic.window_manager - INFO - Stopping WindowManager
2025-06-10 15:22:43 - __main__ - INFO - Application shutdown complete
2025-06-10 15:29:25 - __main__ - INFO - RDP Dashboard Application initialized
2025-06-10 15:29:25 - __main__ - INFO - Starting RDP Dashboard Application...
2025-06-10 15:29:25 - __main__ - INFO - Initializing application components...
2025-06-10 15:29:25 - logic.system_info - INFO - SystemInfoCollector initialized
2025-06-10 15:29:25 - __main__ - INFO - System info collector initialized
2025-06-10 15:29:25 - logic.window_manager - INFO - WindowManager initialized
2025-06-10 15:29:25 - __main__ - INFO - Window manager initialized
2025-06-10 15:29:25 - logic.floating_widget - INFO - FloatingWidget initialized
2025-06-10 15:29:25 - logic.window_manager - INFO - Floating widget initialized
2025-06-10 15:29:25 - ui.widget_ui - INFO - WidgetUI initialized
2025-06-10 15:29:25 - ui.widget_ui - INFO - Dash application created successfully
2025-06-10 15:29:25 - __main__ - INFO - Widget UI initialized
2025-06-10 15:29:25 - __main__ - INFO - All components initialized successfully
2025-06-10 15:29:25 - __main__ - INFO - Starting Dash server...
2025-06-10 15:29:25 - __main__ - INFO - Dash server thread started
2025-06-10 15:29:25 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-06-10 15:29:25 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8050
2025-06-10 15:29:25 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 15:29:27 - logic.window_manager - INFO - System tray icon created successfully
2025-06-10 15:29:27 - logic.window_manager - INFO - Starting system tray
2025-06-10 15:29:27 - logic.window_manager - INFO - System tray started in background thread
2025-06-10 15:29:27 - logic.floating_widget - INFO - Starting floating widget...
2025-06-10 15:29:27 - logic.floating_widget - INFO - Calculated initial position: (2412, 20)
2025-06-10 15:29:27 - logic.floating_widget - ERROR - Error creating floating window: create_window() got an unexpected keyword argument 'shadow'
2025-06-10 15:29:27 - logic.floating_widget - ERROR - Error starting floating widget: create_window() got an unexpected keyword argument 'shadow'
2025-06-10 15:29:27 - logic.window_manager - ERROR - Error starting floating widget: create_window() got an unexpected keyword argument 'shadow'
2025-06-10 15:29:27 - __main__ - INFO - RDP Dashboard Application started successfully
2025-06-10 15:29:27 - __main__ - INFO - Widget available at: http://127.0.0.1:8050
2025-06-10 15:29:27 - __main__ - INFO - Check system tray for options
2025-06-10 15:29:27 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:27] "GET / HTTP/1.1" 200 -
2025-06-10 15:29:28 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:28] "GET /_dash-layout HTTP/1.1" 200 -
2025-06-10 15:29:28 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:28] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-06-10 15:29:28 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:29:29 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:29] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:29:33 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:29:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:34] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:29:38 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:29:39 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:29:43 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:29:44 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:44] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:29:48 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:29:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:49] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:29:53 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:29:54 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:29:54] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:30:30 - __main__ - INFO - RDP Dashboard Application initialized
2025-06-10 15:30:30 - __main__ - INFO - Starting RDP Dashboard Application...
2025-06-10 15:30:30 - __main__ - INFO - Initializing application components...
2025-06-10 15:30:30 - logic.system_info - INFO - SystemInfoCollector initialized
2025-06-10 15:30:30 - __main__ - INFO - System info collector initialized
2025-06-10 15:30:30 - logic.window_manager - INFO - WindowManager initialized
2025-06-10 15:30:30 - __main__ - INFO - Window manager initialized
2025-06-10 15:30:30 - logic.floating_widget - INFO - FloatingWidget initialized
2025-06-10 15:30:30 - logic.window_manager - INFO - Floating widget initialized
2025-06-10 15:30:30 - ui.widget_ui - INFO - WidgetUI initialized
2025-06-10 15:30:31 - ui.widget_ui - INFO - Dash application created successfully
2025-06-10 15:30:31 - __main__ - INFO - Widget UI initialized
2025-06-10 15:30:31 - __main__ - INFO - All components initialized successfully
2025-06-10 15:30:31 - __main__ - INFO - Starting Dash server...
2025-06-10 15:30:31 - __main__ - INFO - Dash server thread started
2025-06-10 15:30:31 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-06-10 15:30:31 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8050
2025-06-10 15:30:31 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 15:30:33 - logic.window_manager - INFO - System tray icon created successfully
2025-06-10 15:30:33 - logic.window_manager - INFO - Starting system tray
2025-06-10 15:30:33 - logic.window_manager - INFO - System tray started in background thread
2025-06-10 15:30:33 - logic.floating_widget - INFO - Starting floating widget...
2025-06-10 15:30:33 - logic.floating_widget - INFO - Calculated initial position: (2412, 20)
2025-06-10 15:30:33 - logic.floating_widget - INFO - Floating widget window created successfully
2025-06-10 15:30:33 - logic.floating_widget - INFO - Floating widget started successfully
2025-06-10 15:30:33 - logic.window_manager - INFO - Floating widget started
2025-06-10 15:30:33 - __main__ - INFO - RDP Dashboard Application started successfully
2025-06-10 15:30:33 - __main__ - INFO - Widget available at: http://127.0.0.1:8050
2025-06-10 15:30:33 - __main__ - INFO - Check system tray for options
2025-06-10 15:30:35 - logic.floating_widget - ERROR - Error running webview: pywebview must be run on a main thread.
2025-06-10 15:32:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:47] "GET / HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET / HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/deps/react-dom@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/deps/prop-types@15.v2_17_1m1749539957.8.1.min.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/deps/react@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/deps/polyfill@7.v2_17_1m1749539957.12.1.min.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash_bootstrap_components/_components/dash_bootstrap_components.v1_5_0m1749539958.min.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/dash-renderer/build/dash_renderer.v2_17_1m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/dcc/dash_core_components-shared.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/dcc/dash_core_components.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/dash_table/bundle.v5_2_11m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:32:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:48] "GET /_dash-component-suites/dash/html/dash_html_components.v2_0_18m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 15:32:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:49] "GET /_dash-layout HTTP/1.1" 200 -
2025-06-10 15:32:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:49] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-06-10 15:32:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:49] "GET /_favicon.ico?v=2.17.1 HTTP/1.1" 200 -
2025-06-10 15:32:49 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:32:50 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:50] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:32:54 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:32:55 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:32:55] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:32:59 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:00 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:04 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:05 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:05] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:09 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:10 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:10] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:14 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:15 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:15] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:19 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:20 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:20] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:24 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:25 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:25] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:29 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:30 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:30] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:34 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:35 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:33:39 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:33:40 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:33:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:12:29 - __main__ - INFO - Received signal 2, shutting down...
2025-06-10 16:12:29 - __main__ - INFO - Shutting down RDP Dashboard Application...
2025-06-10 16:12:29 - logic.window_manager - INFO - Stopping WindowManager
2025-06-10 16:12:29 - __main__ - INFO - Application shutdown complete
2025-06-10 16:13:13 - __main__ - INFO - RDP Dashboard Application initialized
2025-06-10 16:13:13 - __main__ - INFO - Starting RDP Dashboard Application...
2025-06-10 16:13:13 - __main__ - INFO - Initializing application components...
2025-06-10 16:13:13 - logic.system_info - INFO - SystemInfoCollector initialized
2025-06-10 16:13:13 - __main__ - INFO - System info collector initialized
2025-06-10 16:13:13 - logic.window_manager - INFO - WindowManager initialized
2025-06-10 16:13:13 - __main__ - INFO - Window manager initialized
2025-06-10 16:13:13 - logic.floating_widget - INFO - FloatingWidget initialized
2025-06-10 16:13:13 - logic.window_manager - INFO - Floating widget initialized
2025-06-10 16:13:13 - ui.widget_ui - INFO - WidgetUI initialized
2025-06-10 16:13:13 - ui.widget_ui - INFO - Dash application created successfully
2025-06-10 16:13:13 - __main__ - INFO - Widget UI initialized
2025-06-10 16:13:13 - __main__ - INFO - All components initialized successfully
2025-06-10 16:13:13 - __main__ - INFO - Starting Dash server...
2025-06-10 16:13:13 - __main__ - INFO - Dash server thread started
2025-06-10 16:13:13 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-06-10 16:13:13 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8050
2025-06-10 16:13:13 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 16:13:15 - logic.window_manager - INFO - System tray icon created successfully
2025-06-10 16:13:15 - logic.window_manager - INFO - Starting system tray
2025-06-10 16:13:15 - logic.window_manager - INFO - System tray started in background thread
2025-06-10 16:13:15 - logic.floating_widget - INFO - Starting floating widget...
2025-06-10 16:13:15 - logic.floating_widget - INFO - Calculated initial position: (2412, 20)
2025-06-10 16:13:15 - logic.floating_widget - INFO - Floating widget window created successfully
2025-06-10 16:13:15 - logic.floating_widget - INFO - Floating widget started successfully
2025-06-10 16:13:15 - logic.window_manager - INFO - Floating widget started
2025-06-10 16:13:15 - __main__ - INFO - RDP Dashboard Application started successfully
2025-06-10 16:13:15 - __main__ - INFO - Widget available at: http://127.0.0.1:8050
2025-06-10 16:13:15 - __main__ - INFO - Check system tray for options
2025-06-10 16:13:18 - logic.floating_widget - ERROR - Error in delayed webview start: pywebview must be run on a main thread.
2025-06-10 16:13:37 - __main__ - INFO - Received signal 2, shutting down...
2025-06-10 16:13:37 - __main__ - INFO - Shutting down RDP Dashboard Application...
2025-06-10 16:13:37 - logic.window_manager - INFO - Stopping WindowManager
2025-06-10 16:13:37 - __main__ - INFO - Application shutdown complete
2025-06-10 16:15:43 - __main__ - INFO - RDP Dashboard Application initialized
2025-06-10 16:15:43 - __main__ - INFO - Starting RDP Dashboard Application...
2025-06-10 16:15:43 - __main__ - INFO - Initializing application components...
2025-06-10 16:15:43 - logic.system_info - INFO - SystemInfoCollector initialized
2025-06-10 16:15:43 - __main__ - INFO - System info collector initialized
2025-06-10 16:15:43 - logic.window_manager - INFO - WindowManager initialized
2025-06-10 16:15:43 - __main__ - INFO - Window manager initialized
2025-06-10 16:15:43 - logic.simple_floating_widget - INFO - SimpleFloatingWidget initialized
2025-06-10 16:15:43 - logic.window_manager - INFO - Floating widget initialized
2025-06-10 16:15:43 - ui.widget_ui - INFO - WidgetUI initialized
2025-06-10 16:15:43 - ui.widget_ui - INFO - Dash application created successfully
2025-06-10 16:15:43 - __main__ - INFO - Widget UI initialized
2025-06-10 16:15:43 - __main__ - INFO - All components initialized successfully
2025-06-10 16:15:43 - __main__ - INFO - Starting Dash server...
2025-06-10 16:15:43 - __main__ - INFO - Dash server thread started
2025-06-10 16:15:43 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-06-10 16:15:43 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8050
2025-06-10 16:15:43 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 16:15:45 - logic.window_manager - INFO - System tray icon created successfully
2025-06-10 16:15:45 - logic.window_manager - INFO - Starting system tray
2025-06-10 16:15:45 - logic.window_manager - INFO - System tray started in background thread
2025-06-10 16:15:45 - logic.simple_floating_widget - INFO - Starting floating widget in separate process...
2025-06-10 16:15:45 - logic.simple_floating_widget - INFO - Created floating widget script: floating_widget_standalone.py
2025-06-10 16:15:45 - logic.simple_floating_widget - INFO - Floating widget started with PID: 14628
2025-06-10 16:15:45 - logic.window_manager - INFO - Floating widget started
2025-06-10 16:15:45 - __main__ - INFO - RDP Dashboard Application started successfully
2025-06-10 16:15:45 - __main__ - INFO - Widget available at: http://127.0.0.1:8050
2025-06-10 16:15:45 - __main__ - INFO - Check system tray for options
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET / HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/deps/prop-types@15.v2_17_1m1749539957.8.1.min.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/deps/polyfill@7.v2_17_1m1749539957.12.1.min.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/dash-renderer/build/dash_renderer.v2_17_1m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash_bootstrap_components/_components/dash_bootstrap_components.v1_5_0m1749539958.min.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/deps/react-dom@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/deps/react@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/dcc/dash_core_components.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/dcc/dash_core_components-shared.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/html/dash_html_components.v2_0_18m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 16:15:47 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:47] "GET /_dash-component-suites/dash/dash_table/bundle.v5_2_11m1749539957.js HTTP/1.1" 200 -
2025-06-10 16:15:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:48] "GET /_dash-layout HTTP/1.1" 200 -
2025-06-10 16:15:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:48] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-06-10 16:15:48 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:48] "GET /_favicon.ico?v=2.17.1 HTTP/1.1" 200 -
2025-06-10 16:15:48 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:15:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:49] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:15:53 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:15:54 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:54] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:15:58 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:15:59 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:15:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:03 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:04 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:04] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:08 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:09 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:09] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:13 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:14 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:14] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:18 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:19 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:19] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:23 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:24 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:24] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:28 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:29 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:29] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:33 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:34] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:38 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:39 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:43 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:44 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:44] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:48 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:49] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:53 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:54 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:54] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:16:58 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:16:59 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:16:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:03 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:04 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:04] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:08 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:09 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:09] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:13 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:14 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:14] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:18 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:19 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:19] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:23 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:24 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:24] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:28 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:29 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:29] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:33 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:34] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:38 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:39 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:43 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:44 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:44] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:48 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:49] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:53 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:54 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:54] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:17:58 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:17:59 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:17:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:03 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:04 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:04] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:08 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:09 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:09] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:13 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:14 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:14] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:18 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:19 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:19] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:23 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:24 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:24] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:28 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:29 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:29] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:33 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:34] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:38 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:39 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:43 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:44 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:44] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:48 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:49 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:49] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:53 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:54 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:54] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:18:58 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:18:59 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:18:59] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:03 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:04 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:04] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:08 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:09 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:09] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:13 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:14 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:14] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:18 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:19 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:19] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:23 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:24 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:24] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:28 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:29 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:29] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:33 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:34] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:38 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:39 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:39] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:43 - logic.system_info - INFO - Collecting all system information
2025-06-10 16:19:44 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 16:19:44] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 16:19:54 - __main__ - INFO - Received signal 2, shutting down...
2025-06-10 16:19:54 - __main__ - INFO - Shutting down RDP Dashboard Application...
2025-06-10 16:19:54 - logic.window_manager - INFO - Stopping WindowManager
2025-06-10 16:19:54 - __main__ - INFO - Application shutdown complete
