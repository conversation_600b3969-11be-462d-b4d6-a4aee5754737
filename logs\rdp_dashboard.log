2025-06-10 15:19:53 - __main__ - INFO - RDP Dashboard Application initialized
2025-06-10 15:19:53 - __main__ - INFO - Starting RDP Dashboard Application...
2025-06-10 15:19:53 - __main__ - INFO - Initializing application components...
2025-06-10 15:19:53 - logic.system_info - INFO - SystemInfoCollector initialized
2025-06-10 15:19:53 - __main__ - INFO - System info collector initialized
2025-06-10 15:19:53 - logic.window_manager - INFO - WindowManager initialized
2025-06-10 15:19:53 - __main__ - INFO - Window manager initialized
2025-06-10 15:19:53 - ui.widget_ui - INFO - WidgetUI initialized
2025-06-10 15:19:53 - ui.widget_ui - INFO - Dash application created successfully
2025-06-10 15:19:53 - __main__ - INFO - Widget UI initialized
2025-06-10 15:19:53 - __main__ - INFO - All components initialized successfully
2025-06-10 15:19:53 - __main__ - INFO - Starting Dash server...
2025-06-10 15:19:53 - __main__ - INFO - Dash server thread started
2025-06-10 15:19:53 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-06-10 15:19:53 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8050
2025-06-10 15:19:53 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 15:19:55 - logic.window_manager - INFO - System tray icon created successfully
2025-06-10 15:19:55 - logic.window_manager - INFO - Starting system tray
2025-06-10 15:19:55 - logic.window_manager - INFO - System tray started in background thread
2025-06-10 15:19:55 - __main__ - INFO - RDP Dashboard Application started successfully
2025-06-10 15:19:55 - __main__ - INFO - Widget available at: http://127.0.0.1:8050
2025-06-10 15:19:55 - __main__ - INFO - Check system tray for options
2025-06-10 15:20:27 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:27] "GET / HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET / HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/react@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/prop-types@15.v2_17_1m1749539957.8.1.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dash-renderer/build/dash_renderer.v2_17_1m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash_bootstrap_components/_components/dash_bootstrap_components.v1_5_0m1749539958.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dcc/dash_core_components-shared.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dash_table/bundle.v5_2_11m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/react-dom@16.v2_17_1m1749539957.14.0.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/deps/polyfill@7.v2_17_1m1749539957.12.1.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/dcc/dash_core_components.v2_14_1m1749539957.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-component-suites/dash/html/dash_html_components.v2_0_18m1749539957.min.js HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-layout HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-06-10 15:20:34 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:34] "GET /_favicon.ico?v=2.17.1 HTTP/1.1" 200 -
2025-06-10 15:20:34 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:35 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:39 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:40 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:40] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:44 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:45 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:45] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:49 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:50 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:50] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:54 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:20:55 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:20:55] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:20:59 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:21:00 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:21:00] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:21:04 - logic.system_info - INFO - Collecting all system information
2025-06-10 15:21:05 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 15:21:05] "POST /_dash-update-component HTTP/1.1" 200 -
2025-06-10 15:22:43 - __main__ - INFO - Received signal 2, shutting down...
2025-06-10 15:22:43 - __main__ - INFO - Shutting down RDP Dashboard Application...
2025-06-10 15:22:43 - logic.window_manager - INFO - Stopping WindowManager
2025-06-10 15:22:43 - __main__ - INFO - Application shutdown complete
