@echo off
echo ========================================
echo RDP Dashboard - Running Unit Tests
echo ========================================
echo.

REM Activate conda environment
call conda activate rdp-dashboard
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to activate conda environment
    echo Please run install.bat first
    pause
    exit /b 1
)

echo Running unit tests...
echo.

REM Run tests with pytest if available, otherwise use unittest
python -m pytest tests/ -v 2>nul
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo All tests passed successfully!
    echo ========================================
) else (
    echo.
    echo Pytest not available, trying unittest...
    python tests/test_system_info.py
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo All tests passed successfully!
        echo ========================================
    ) else (
        echo.
        echo ========================================
        echo Some tests failed. Check output above.
        echo ========================================
    )
)

echo.
pause
