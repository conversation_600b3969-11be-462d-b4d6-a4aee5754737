/* RDP Dashboard Widget Styles */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
}

/* Main Container */
.widget-container {
    width: 280px;
    max-height: 90vh;
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(50, 50, 50, 0.95));
    border: 1px solid rgba(100, 100, 100, 0.3);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    overflow-y: auto;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

/* Header */
.widget-header {
    background: linear-gradient(90deg, #2196F3, #21CBF3);
    padding: 10px 15px;
    border-radius: 8px 8px 0 0;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Content Sections */
.info-section {
    margin: 15px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 3px solid #2196F3;
}

.section-title {
    font-size: 13px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Info Items */
.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    padding: 4px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: #cccccc;
    flex: 1;
    margin-right: 10px;
}

.info-value {
    color: #ffffff;
    text-align: right;
    flex: 1;
    word-break: break-all;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
}

/* Network Interface Styles */
.network-interface {
    background: rgba(33, 150, 243, 0.1);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    border-left: 2px solid #2196F3;
}

.interface-name {
    font-weight: bold;
    color: #2196F3;
    font-size: 12px;
    margin-bottom: 4px;
}

.interface-details {
    font-size: 11px;
    color: #cccccc;
}

/* Performance Metrics */
.performance-metric {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.metric-label {
    flex: 1;
    color: #cccccc;
}

.metric-value {
    flex: 1;
    text-align: right;
    font-weight: bold;
    color: #ffffff;
}

.metric-bar {
    width: 60px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    margin-left: 8px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FFC107, #F44336);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Status Indicators */
.status-online {
    color: #4CAF50;
    font-weight: bold;
}

.status-offline {
    color: #F44336;
    font-weight: bold;
}

.status-warning {
    color: #FFC107;
    font-weight: bold;
}

/* Timestamp */
.timestamp {
    text-align: center;
    font-size: 10px;
    color: #888888;
    padding: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 8px 8px;
}

/* Scrollbar Styling */
.widget-container::-webkit-scrollbar {
    width: 6px;
}

.widget-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.widget-container::-webkit-scrollbar-thumb {
    background: rgba(33, 150, 243, 0.6);
    border-radius: 3px;
}

.widget-container::-webkit-scrollbar-thumb:hover {
    background: rgba(33, 150, 243, 0.8);
}

/* Responsive Design */
@media (max-height: 800px) {
    .widget-container {
        max-height: 85vh;
    }
    
    .info-section {
        margin: 10px;
        padding: 8px;
    }
}

/* Animation for updates */
.updating {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Hover effects */
.info-section:hover {
    background: rgba(255, 255, 255, 0.08);
    transition: background 0.2s ease;
}

/* Error states */
.error-message {
    color: #F44336;
    font-style: italic;
    text-align: center;
    padding: 10px;
    background: rgba(244, 67, 54, 0.1);
    border-radius: 4px;
    margin: 10px;
}
