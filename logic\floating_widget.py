"""
Floating Widget Module

This module creates a true floating desktop widget using webview
that can be dragged around, stays on top, and provides a native
window experience.

Author: RDP Dashboard Team
"""

import threading
import time
import logging
import json
import os
from typing import Tuple
import webview


class FloatingWidget:
    """
    Creates and manages a floating desktop widget window.
    
    This class uses pywebview to create a native floating window
    that displays the Dash application content in a draggable,
    always-on-top widget.
    """
    
    def __init__(self, app_instance=None, widget_url="http://127.0.0.1:8050"):
        """
        Initialize the FloatingWidget.
        
        Args:
            app_instance: Reference to the main application instance
            widget_url: URL of the Dash application
        """
        self.logger = logging.getLogger(__name__)
        self.app_instance = app_instance
        self.widget_url = widget_url
        self.window = None
        self.is_running = False
        
        # Widget configuration
        self.widget_width = 320
        self.widget_height = 650
        self.widget_x = None
        self.widget_y = None
        
        # Load saved position if available
        self.load_widget_position()
        
        self.logger.info("FloatingWidget initialized")
    
    def load_widget_position(self):
        """Load the last saved widget position from file."""
        try:
            position_file = "widget_position.json"
            if os.path.exists(position_file):
                with open(position_file, 'r') as f:
                    position_data = json.load(f)
                    self.widget_x = position_data.get('x')
                    self.widget_y = position_data.get('y')
                    self.logger.info(f"Loaded widget position: ({self.widget_x}, {self.widget_y})")
        except Exception as e:
            self.logger.warning(f"Could not load widget position: {e}")
    
    def save_widget_position(self):
        """Save the current widget position to file."""
        try:
            if self.window:
                # Get current window position
                # Note: webview doesn't provide direct position access,
                # so we'll save the last known position
                position_data = {
                    'x': self.widget_x,
                    'y': self.widget_y
                }
                
                position_file = "widget_position.json"
                with open(position_file, 'w') as f:
                    json.dump(position_data, f)
                    
                self.logger.info(f"Saved widget position: ({self.widget_x}, {self.widget_y})")
        except Exception as e:
            self.logger.warning(f"Could not save widget position: {e}")
    
    def calculate_initial_position(self) -> Tuple[int, int]:
        """
        Calculate the initial position for the floating widget.
        
        Returns:
            Tuple[int, int]: X and Y coordinates for widget positioning
        """
        if self.widget_x is not None and self.widget_y is not None:
            return self.widget_x, self.widget_y
        
        try:
            # Get screen dimensions
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()
            
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            _ = screen_height  # Mark as intentionally unused for now

            root.destroy()
            
            # Position on the right side with margin
            margin = 20
            x_position = screen_width - self.widget_width - margin
            y_position = margin
            
            self.widget_x = x_position
            self.widget_y = y_position
            
            self.logger.info(f"Calculated initial position: ({x_position}, {y_position})")
            return x_position, y_position
            
        except Exception as e:
            self.logger.error(f"Error calculating position: {e}")
            # Default position
            self.widget_x = 100
            self.widget_y = 100
            return 100, 100
    
    def create_floating_window(self):
        """Create the floating widget window."""
        try:
            # Calculate position
            x_pos, y_pos = self.calculate_initial_position()
            
            # Create the webview window
            self.window = webview.create_window(
                title="RDP Dashboard",
                url=self.widget_url,
                width=self.widget_width,
                height=self.widget_height,
                x=x_pos,
                y=y_pos,
                min_size=(280, 400),
                resizable=True,
                on_top=True,
                # Note: Some parameters may not be supported in all webview versions
                # shadow=True,  # Not supported in this version
                # transparent=False,  # May not be supported
                # frameless=False,  # May not be supported
            )
            
            # Set window properties
            self.setup_window_properties()
            
            self.logger.info("Floating widget window created successfully")
            
        except Exception as e:
            self.logger.error(f"Error creating floating window: {e}")
            raise
    
    def setup_window_properties(self):
        """Set up additional window properties and event handlers."""
        try:
            # Add custom CSS for the floating widget
            custom_js = """
            // Add floating widget specific styles
            const style = document.createElement('style');
            style.textContent = `
                body {
                    margin: 0;
                    padding: 0;
                    overflow: hidden;
                    background: rgba(30, 30, 30, 0.95);
                    backdrop-filter: blur(10px);
                }
                
                .widget-container {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    margin: 0 !important;
                    border-radius: 12px;
                    overflow-y: auto;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                }
                
                .widget-header {
                    cursor: move;
                    user-select: none;
                    background: linear-gradient(90deg, #2196F3, #21CBF3);
                    border-radius: 12px 12px 0 0;
                }
                
                /* Custom scrollbar for floating widget */
                .widget-container::-webkit-scrollbar {
                    width: 8px;
                }
                
                .widget-container::-webkit-scrollbar-track {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 4px;
                }
                
                .widget-container::-webkit-scrollbar-thumb {
                    background: rgba(33, 150, 243, 0.6);
                    border-radius: 4px;
                }
                
                .widget-container::-webkit-scrollbar-thumb:hover {
                    background: rgba(33, 150, 243, 0.8);
                }
            `;
            document.head.appendChild(style);
            
            // Add window controls
            window.addEventListener('beforeunload', function() {
                // Save position before closing
                pywebview.api.save_position();
            });
            """
            
            # Note: This JavaScript will be injected when the window loads
            self.custom_js = custom_js
            
        except Exception as e:
            self.logger.warning(f"Could not set up window properties: {e}")
    
    def on_window_loaded(self):
        """Called when the window has finished loading."""
        try:
            # Inject custom JavaScript
            if self.window and hasattr(self.window, 'evaluate_js'):
                self.window.evaluate_js(self.custom_js)
            
            self.logger.info("Window loaded and customized")
            
        except Exception as e:
            self.logger.warning(f"Error in window loaded callback: {e}")
    
    def start_floating_widget(self):
        """Start the floating widget."""
        try:
            self.logger.info("Starting floating widget...")

            # Create the window
            self.create_floating_window()

            # For now, just create the window definition
            # The actual webview.start() will be called from the main thread
            self.is_running = True
            self.logger.info("Floating widget started successfully")

            # Schedule webview start for later
            self._schedule_webview_start()

        except Exception as e:
            self.logger.error(f"Error starting floating widget: {e}")
            raise

    def _schedule_webview_start(self):
        """Schedule webview start in a way that works with threading."""
        try:
            # Use a simple approach - start webview after a delay
            def delayed_start():
                time.sleep(3)  # Wait for Dash server to be ready
                try:
                    # Start webview - this should work better
                    webview.start(debug=False)
                except Exception as e:
                    self.logger.error(f"Error in delayed webview start: {e}")

            # Start in a thread
            start_thread = threading.Thread(target=delayed_start, daemon=True)
            start_thread.start()

        except Exception as e:
            self.logger.error(f"Error scheduling webview start: {e}")
    
    def _run_webview(self):
        """Run the webview in a separate thread."""
        try:
            # Wait a moment for the Dash server to be ready
            time.sleep(2)
            
            # Start the webview
            webview.start(
                debug=False,
                http_server=False,  # We're using external Dash server
                private_mode=False
            )
            
        except Exception as e:
            self.logger.error(f"Error running webview: {e}")
    
    def show_widget(self):
        """Show the floating widget window."""
        try:
            if self.window:
                self.window.show()
                self.logger.info("Floating widget shown")
        except Exception as e:
            self.logger.error(f"Error showing widget: {e}")
    
    def hide_widget(self):
        """Hide the floating widget window."""
        try:
            if self.window:
                self.window.hide()
                self.logger.info("Floating widget hidden")
        except Exception as e:
            self.logger.error(f"Error hiding widget: {e}")
    
    def toggle_widget(self):
        """Toggle the floating widget visibility."""
        try:
            if self.window:
                # Note: webview doesn't have a direct visibility check
                # This is a simplified toggle
                self.show_widget()
                self.logger.info("Toggled floating widget")
        except Exception as e:
            self.logger.error(f"Error toggling widget: {e}")
    
    def close_widget(self):
        """Close the floating widget."""
        try:
            self.save_widget_position()
            
            if self.window:
                self.window.destroy()
                self.window = None
            
            self.is_running = False
            self.logger.info("Floating widget closed")
            
        except Exception as e:
            self.logger.error(f"Error closing widget: {e}")
    
    def update_position(self, x: int, y: int):
        """
        Update the widget position.
        
        Args:
            x: X coordinate
            y: Y coordinate
        """
        self.widget_x = x
        self.widget_y = y
        self.save_widget_position()
    
    def get_widget_info(self) -> dict:
        """
        Get information about the floating widget.
        
        Returns:
            dict: Widget information
        """
        return {
            'is_running': self.is_running,
            'width': self.widget_width,
            'height': self.widget_height,
            'x': self.widget_x,
            'y': self.widget_y,
            'url': self.widget_url
        }
