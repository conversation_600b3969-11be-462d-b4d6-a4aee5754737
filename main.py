"""
RDP Dashboard - Desktop Widget Application

A lightweight Windows desktop widget that displays system information
including network interfaces, computer details, operating system info,
user information, and system performance metrics.

This application creates a desktop overlay widget positioned on the right
side of the screen with real-time system information updates.

Features:
- Network interface monitoring with IP addresses
- Computer name and system information
- Operating system details
- Current user information
- System performance metrics (CPU, Memory, Disk)
- System tray integration
- Auto-refresh functionality
- Modern, translucent UI design

Author: RDP Dashboard Team
Version: 1.0.0
"""

import sys
import os
import logging
import threading
import time
import signal
# from typing import Optional  # Not used in current implementation
import colorlog

# Add project directories to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logic'))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ui'))

from logic.system_info import SystemInfoCollector
from logic.window_manager import WindowManager
from ui.widget_ui import WidgetUI


class RDPDashboardApp:
    """
    Main application class for the RDP Dashboard desktop widget.
    
    This class orchestrates the system information collection, UI display,
    window management, and system tray integration.
    """
    
    def __init__(self):
        """Initialize the RDP Dashboard application."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.system_info_collector = None
        self.window_manager = None
        self.widget_ui = None
        self.dash_app = None
        
        # Application state
        self.is_running = False
        self.server_thread = None
        
        self.logger.info("RDP Dashboard Application initialized")
    
    def setup_logging(self):
        """Set up logging configuration with colors and proper formatting."""
        # Create logs directory if it doesn't exist
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Configure logging format
        log_format = (
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Create color formatter
        formatter = colorlog.ColoredFormatter(
            log_format,
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        
        # Configure root logger
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler
        file_handler = logging.FileHandler(
            os.path.join(log_dir, 'rdp_dashboard.log'),
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    def initialize_components(self):
        """Initialize all application components."""
        try:
            self.logger.info("Initializing application components...")
            
            # Initialize system info collector
            self.system_info_collector = SystemInfoCollector()
            self.logger.info("System info collector initialized")
            
            # Initialize window manager
            self.window_manager = WindowManager(app_instance=self)
            self.logger.info("Window manager initialized")
            
            # Initialize UI
            self.widget_ui = WidgetUI(self.system_info_collector)
            self.dash_app = self.widget_ui.create_app()
            self.logger.info("Widget UI initialized")
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing components: {e}")
            raise
    
    def start_dash_server(self):
        """Start the Dash server in a separate thread."""
        try:
            self.logger.info("Starting Dash server...")
            
            # Configure server settings
            self.dash_app.run_server(
                debug=False,
                host='127.0.0.1',
                port=8050,
                use_reloader=False,
                dev_tools_hot_reload=False,
                dev_tools_ui=False,
                dev_tools_props_check=False
            )
            
        except Exception as e:
            self.logger.error(f"Error starting Dash server: {e}")
            raise

    def start_server_thread(self):
        """Start the Dash server in a background thread."""
        try:
            self.server_thread = threading.Thread(
                target=self.start_dash_server,
                daemon=True
            )
            self.server_thread.start()
            self.logger.info("Dash server thread started")

            # Wait a moment for server to start
            time.sleep(2)

        except Exception as e:
            self.logger.error(f"Error starting server thread: {e}")
            raise

    def refresh_data(self):
        """Refresh system data (called from system tray menu)."""
        try:
            self.logger.info("Refreshing system data...")
            # The data refresh is handled automatically by the Dash callbacks
            # This method is here for future enhancements

        except Exception as e:
            self.logger.error(f"Error refreshing data: {e}")

    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            """Handle system signals for graceful shutdown."""
            _ = frame  # Unused parameter
            self.logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def run(self):
        """Run the main application."""
        try:
            self.logger.info("Starting RDP Dashboard Application...")

            # Initialize components
            self.initialize_components()

            # Set up signal handlers
            self.setup_signal_handlers()

            # Start Dash server in background thread
            self.start_server_thread()

            # Start system tray
            self.window_manager.start_system_tray()

            # Set running flag
            self.is_running = True

            self.logger.info("RDP Dashboard Application started successfully")
            self.logger.info("Widget available at: http://127.0.0.1:8050")
            self.logger.info("Check system tray for options")

            # Keep main thread alive
            try:
                while self.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Keyboard interrupt received")
                self.shutdown()

        except Exception as e:
            self.logger.error(f"Error running application: {e}")
            self.shutdown()
            sys.exit(1)

    def shutdown(self):
        """Shutdown the application gracefully."""
        self.logger.info("Shutting down RDP Dashboard Application...")

        self.is_running = False

        # Stop window manager
        if self.window_manager:
            self.window_manager.stop()

        # Note: Dash server will be stopped when the main thread exits

        self.logger.info("Application shutdown complete")


def main():
    """Main entry point for the application."""
    print("=" * 50)
    print("RDP Dashboard - Desktop Widget")
    print("Version 1.0.0")
    print("=" * 50)
    print()

    try:
        # Create and run the application
        app = RDPDashboardApp()
        app.run()

    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
