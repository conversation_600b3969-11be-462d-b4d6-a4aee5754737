@echo off
echo ========================================
echo RDP Dashboard - Clean Installation
echo ========================================
echo.

REM Check if conda is installed
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Conda is not installed or not in PATH
    echo Please install Anaconda or Miniconda first
    pause
    exit /b 1
)

echo Step 1: Removing existing environment (if any)...
conda env remove -n rdp-dashboard -y 2>nul
echo.

echo Step 2: Creating fresh conda environment with Python 3.12...
conda create -n rdp-dashboard python=3.12 -y

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create conda environment
    pause
    exit /b 1
)

echo.
echo Step 3: Activating environment...
call conda activate rdp-dashboard

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to activate conda environment
    pause
    exit /b 1
)

echo.
echo Step 4: Upgrading pip...
python -m pip install --upgrade pip

echo.
echo Step 5: Installing core packages individually...

echo Installing Dash framework...
pip install dash==2.17.1
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to install dash
)

echo Installing Dash Bootstrap Components...
pip install dash-bootstrap-components==1.5.0
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to install dash-bootstrap-components
)

echo Installing psutil (system information)...
pip install psutil==5.9.6
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to install psutil
)

echo Installing Windows-specific packages...
pip install pywin32==306
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to install pywin32
)

pip install pystray==0.19.5
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to install pystray
)

pip install Pillow==10.1.0
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to install Pillow
)

echo Installing logging support...
pip install colorlog==6.8.0
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Failed to install colorlog
)

echo.
echo Step 6: Attempting to install netifaces (optional)...
pip install netifaces==0.11.0
if %ERRORLEVEL% NEQ 0 (
    echo NOTE: netifaces installation failed - this is OK!
    echo The application will use psutil for network information instead.
)

echo.
echo Step 7: Installing optional testing packages...
pip install pytest==7.4.3 pytest-cov==4.1.0
if %ERRORLEVEL% NEQ 0 (
    echo NOTE: Testing packages failed to install - this is OK for basic usage
)

echo.
echo ========================================
echo Installation Summary
echo ========================================

echo.
echo Testing installation...
python -c "import dash, psutil, pystray; print('✓ Core packages installed successfully')"
if %ERRORLEVEL% EQU 0 (
    echo.
    echo 🎉 INSTALLATION SUCCESSFUL!
    echo.
    echo Core packages are installed and working.
    echo You can now run the application with:
    echo   python main.py
    echo.
    echo Or use the launcher:
    echo   launcher.bat
) else (
    echo.
    echo ❌ INSTALLATION ISSUES DETECTED
    echo.
    echo Some core packages may not be working properly.
    echo Please check the error messages above.
)

echo.
echo ========================================
pause
