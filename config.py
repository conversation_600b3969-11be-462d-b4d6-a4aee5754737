"""
RDP Dashboard - Configuration Settings

This module contains configuration settings for the RDP Dashboard
desktop widget application. Modify these values to customize
the application behavior.

Author: RDP Dashboard Team
"""

# Application Information
APP_NAME = "RDP Dashboard"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Desktop System Information Widget"

# Server Configuration
SERVER_HOST = "127.0.0.1"
SERVER_PORT = 8050
SERVER_DEBUG = False

# Widget Configuration
WIDGET_WIDTH = 300
WIDGET_HEIGHT = 600
WIDGET_MARGIN = 20  # Margin from screen edge

# Update Intervals (in milliseconds)
REFRESH_INTERVAL = 5000  # 5 seconds
PERFORMANCE_UPDATE_INTERVAL = 1000  # 1 second for CPU monitoring

# UI Configuration
WIDGET_OPACITY = 0.95
WIDGET_ALWAYS_ON_TOP = True
WIDGET_POSITION = "right"  # "right", "left", "top-right", "top-left"

# Logging Configuration
LOG_LEVEL = "INFO"  # "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
LOG_FILE_MAX_SIZE = 10 * 1024 * 1024  # 10 MB
LOG_FILE_BACKUP_COUNT = 5

# System Information Configuration
SHOW_IPV6_ADDRESSES = True
SHOW_LOOPBACK_INTERFACES = False
SHOW_INACTIVE_INTERFACES = False
MAX_INTERFACE_NAME_LENGTH = 20

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING = True
CPU_SAMPLE_INTERVAL = 1.0  # seconds
MEMORY_THRESHOLD_WARNING = 80  # percentage
DISK_THRESHOLD_WARNING = 90  # percentage

# System Tray Configuration
ENABLE_SYSTEM_TRAY = True
TRAY_ICON_SIZE = (16, 16)
SHOW_NOTIFICATIONS = True

# Theme Configuration
THEME = "dark"  # "dark", "light"
PRIMARY_COLOR = "#2196F3"
SECONDARY_COLOR = "#21CBF3"
SUCCESS_COLOR = "#4CAF50"
WARNING_COLOR = "#FFC107"
ERROR_COLOR = "#F44336"

# Advanced Configuration
ENABLE_AUTO_START = False  # Start with Windows (requires setup)
MINIMIZE_TO_TRAY_ON_CLOSE = True
REMEMBER_WINDOW_POSITION = True
CHECK_FOR_UPDATES = False

# Network Configuration
NETWORK_TIMEOUT = 5  # seconds
MAX_NETWORK_INTERFACES = 10
SHOW_NETWORK_STATISTICS = True

# Security Configuration
ALLOW_REMOTE_ACCESS = False  # Only allow localhost by default
REQUIRE_AUTHENTICATION = False

# Export configuration as a dictionary for easy access
CONFIG = {
    "app": {
        "name": APP_NAME,
        "version": APP_VERSION,
        "description": APP_DESCRIPTION
    },
    "server": {
        "host": SERVER_HOST,
        "port": SERVER_PORT,
        "debug": SERVER_DEBUG
    },
    "widget": {
        "width": WIDGET_WIDTH,
        "height": WIDGET_HEIGHT,
        "margin": WIDGET_MARGIN,
        "opacity": WIDGET_OPACITY,
        "always_on_top": WIDGET_ALWAYS_ON_TOP,
        "position": WIDGET_POSITION
    },
    "intervals": {
        "refresh": REFRESH_INTERVAL,
        "performance": PERFORMANCE_UPDATE_INTERVAL
    },
    "logging": {
        "level": LOG_LEVEL,
        "max_size": LOG_FILE_MAX_SIZE,
        "backup_count": LOG_FILE_BACKUP_COUNT
    },
    "system_info": {
        "show_ipv6": SHOW_IPV6_ADDRESSES,
        "show_loopback": SHOW_LOOPBACK_INTERFACES,
        "show_inactive": SHOW_INACTIVE_INTERFACES,
        "max_interface_name_length": MAX_INTERFACE_NAME_LENGTH
    },
    "performance": {
        "enabled": ENABLE_PERFORMANCE_MONITORING,
        "cpu_interval": CPU_SAMPLE_INTERVAL,
        "memory_warning": MEMORY_THRESHOLD_WARNING,
        "disk_warning": DISK_THRESHOLD_WARNING
    },
    "tray": {
        "enabled": ENABLE_SYSTEM_TRAY,
        "icon_size": TRAY_ICON_SIZE,
        "notifications": SHOW_NOTIFICATIONS
    },
    "theme": {
        "name": THEME,
        "primary": PRIMARY_COLOR,
        "secondary": SECONDARY_COLOR,
        "success": SUCCESS_COLOR,
        "warning": WARNING_COLOR,
        "error": ERROR_COLOR
    },
    "advanced": {
        "auto_start": ENABLE_AUTO_START,
        "minimize_to_tray": MINIMIZE_TO_TRAY_ON_CLOSE,
        "remember_position": REMEMBER_WINDOW_POSITION,
        "check_updates": CHECK_FOR_UPDATES
    },
    "network": {
        "timeout": NETWORK_TIMEOUT,
        "max_interfaces": MAX_NETWORK_INTERFACES,
        "show_statistics": SHOW_NETWORK_STATISTICS
    },
    "security": {
        "allow_remote": ALLOW_REMOTE_ACCESS,
        "require_auth": REQUIRE_AUTHENTICATION
    }
}


def get_config(section=None, key=None):
    """
    Get configuration value(s).
    
    Args:
        section (str, optional): Configuration section name
        key (str, optional): Configuration key name
        
    Returns:
        dict or any: Configuration value(s)
    """
    if section is None:
        return CONFIG
    
    if section not in CONFIG:
        raise KeyError(f"Configuration section '{section}' not found")
    
    if key is None:
        return CONFIG[section]
    
    if key not in CONFIG[section]:
        raise KeyError(f"Configuration key '{key}' not found in section '{section}'")
    
    return CONFIG[section][key]


def update_config(section, key, value):
    """
    Update a configuration value.
    
    Args:
        section (str): Configuration section name
        key (str): Configuration key name
        value (any): New value
    """
    if section not in CONFIG:
        CONFIG[section] = {}
    
    CONFIG[section][key] = value


# Configuration validation
def validate_config():
    """Validate configuration values and set defaults if needed."""
    # Validate server port
    if not (1024 <= CONFIG["server"]["port"] <= 65535):
        print(f"Warning: Invalid server port {CONFIG['server']['port']}, using default 8050")
        CONFIG["server"]["port"] = 8050
    
    # Validate refresh interval
    if CONFIG["intervals"]["refresh"] < 1000:
        print(f"Warning: Refresh interval too low, setting to minimum 1000ms")
        CONFIG["intervals"]["refresh"] = 1000
    
    # Validate widget dimensions
    if CONFIG["widget"]["width"] < 200:
        CONFIG["widget"]["width"] = 200
    if CONFIG["widget"]["height"] < 400:
        CONFIG["widget"]["height"] = 400
    
    # Validate opacity
    if not (0.1 <= CONFIG["widget"]["opacity"] <= 1.0):
        CONFIG["widget"]["opacity"] = 0.95


# Run validation on import
validate_config()
