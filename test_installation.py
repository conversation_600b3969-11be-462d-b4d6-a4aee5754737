"""
Installation Test Script

This script tests if all required dependencies are properly installed
and can be imported successfully.

Run this after installation to verify everything is working.
"""

import sys
import importlib

def test_import(module_name, description=""):
    """Test if a module can be imported."""
    try:
        importlib.import_module(module_name)
        print(f"✓ {module_name} - OK {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - FAILED: {e}")
        return False

def main():
    """Test all required imports."""
    print("=" * 50)
    print("RDP Dashboard - Installation Test")
    print("=" * 50)
    print()
    
    # Test core Python modules
    print("Testing Core Python Modules:")
    core_modules = [
        ("sys", ""),
        ("os", ""),
        ("logging", ""),
        ("threading", ""),
        ("socket", ""),
        ("platform", ""),
        ("getpass", ""),
        ("datetime", ""),
        ("json", ""),
    ]
    
    core_success = 0
    for module, desc in core_modules:
        if test_import(module, desc):
            core_success += 1
    
    print(f"\nCore modules: {core_success}/{len(core_modules)} successful")
    print()
    
    # Test required packages
    print("Testing Required Packages:")
    required_packages = [
        ("dash", "- Web framework"),
        ("dash_bootstrap_components", "- UI components"),
        ("psutil", "- System information"),
        ("colorlog", "- Colored logging"),
    ]
    
    required_success = 0
    for module, desc in required_packages:
        if test_import(module, desc):
            required_success += 1
    
    print(f"\nRequired packages: {required_success}/{len(required_packages)} successful")
    print()
    
    # Test Windows-specific packages
    print("Testing Windows-specific Packages:")
    windows_packages = [
        ("win32api", "- Windows API"),
        ("pystray", "- System tray"),
        ("PIL", "- Image processing"),
    ]
    
    windows_success = 0
    for module, desc in windows_packages:
        if test_import(module, desc):
            windows_success += 1
    
    print(f"\nWindows packages: {windows_success}/{len(windows_packages)} successful")
    print()
    
    # Test optional packages
    print("Testing Optional Packages:")
    optional_packages = [
        ("netifaces", "- Network interfaces (optional)"),
        ("pytest", "- Testing framework (optional)"),
    ]
    
    optional_success = 0
    for module, desc in optional_packages:
        if test_import(module, desc):
            optional_success += 1
    
    print(f"\nOptional packages: {optional_success}/{len(optional_packages)} successful")
    print()
    
    # Test our custom modules
    print("Testing Application Modules:")
    try:
        sys.path.append('.')
        sys.path.append('logic')
        sys.path.append('ui')
        
        app_modules = [
            ("logic.system_info", "- System information collector"),
            ("logic.window_manager", "- Window manager"),
            ("ui.widget_ui", "- Widget UI"),
        ]
        
        app_success = 0
        for module, desc in app_modules:
            if test_import(module, desc):
                app_success += 1
        
        print(f"\nApplication modules: {app_success}/{len(app_modules)} successful")
        
    except Exception as e:
        print(f"❌ Error testing application modules: {e}")
        app_success = 0
    
    print()
    print("=" * 50)
    
    # Summary
    total_required = len(core_modules) + len(required_packages) + len(windows_packages)
    total_success = core_success + required_success + windows_success + app_success
    
    if total_success >= total_required:
        print("🎉 INSTALLATION TEST PASSED!")
        print("All required dependencies are installed correctly.")
        print("You can now run the RDP Dashboard application.")
        
        if optional_success < len(optional_packages):
            print()
            print("Note: Some optional packages are missing, but the app should still work.")
            if not test_import("netifaces", ""):
                print("- Network info will use psutil fallback instead of netifaces")
        
        return 0
    else:
        print("❌ INSTALLATION TEST FAILED!")
        print(f"Only {total_success}/{total_required} required dependencies are working.")
        print()
        print("Please check the error messages above and:")
        print("1. Make sure you activated the conda environment")
        print("2. Try running: pip install -r requirements.txt")
        print("3. Or try: pip install -r requirements-minimal.txt")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
