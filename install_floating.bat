@echo off
echo ========================================
echo RDP Dashboard - Floating Widget Setup
echo ========================================
echo.

REM Check if conda environment exists
call conda activate rdp-dashboard 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: rdp-dashboard environment not found
    echo Please run install_clean.bat first
    pause
    exit /b 1
)

echo Installing floating widget dependencies...
echo.

echo Installing pywebview for floating widget functionality...
pip install pywebview==4.4.1

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Floating widget dependencies installed successfully!
    echo.
    echo Testing floating widget functionality...
    python -c "import webview; print('✓ pywebview imported successfully')"
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 🎉 FLOATING WIDGET READY!
        echo.
        echo You can now use the floating widget features:
        echo - Draggable desktop widget
        echo - Always on top functionality
        echo - Native window experience
        echo.
        echo To start with floating widget:
        echo   python main.py
        echo.
        echo Use the system tray menu to control the floating widget.
    ) else (
        echo.
        echo ⚠️  WARNING: pywebview installation may have issues
        echo The application will fall back to browser mode.
    )
) else (
    echo.
    echo ❌ Failed to install pywebview
    echo.
    echo The application will work in browser mode without floating widget.
    echo This is normal and the app will function correctly.
)

echo.
echo ========================================
pause
