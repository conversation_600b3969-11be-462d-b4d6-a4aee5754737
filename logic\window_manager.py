"""
Window Manager Module

This module handles window positioning, system tray integration,
and desktop overlay functionality for the RDP Dashboard widget.

Author: RDP Dashboard Team
"""

import sys
import threading
import logging
from typing import <PERSON>ple
import webbrowser
from PIL import Image, ImageDraw
import pystray
from pystray import MenuItem as item

# Import floating widget if available
try:
    from .floating_widget import FloatingWidget
    HAS_FLOATING_WIDGET = True
except ImportError:
    HAS_FLOATING_WIDGET = False
    print("Warning: webview not available, floating widget disabled")


class WindowManager:
    """
    Manages window positioning and system tray integration.
    
    This class handles the desktop overlay positioning, system tray icon,
    and window management for the RDP Dashboard widget.
    """
    
    def __init__(self, app_instance=None):
        """
        Initialize the WindowManager.

        Args:
            app_instance: Reference to the main application instance
        """
        self.logger = logging.getLogger(__name__)
        self.app_instance = app_instance
        self.tray_icon = None
        self.is_running = False
        self.widget_url = "http://127.0.0.1:8050"

        # Floating widget instance
        self.floating_widget = None
        self.use_floating_widget = HAS_FLOATING_WIDGET

        self.logger.info("WindowManager initialized")
    
    def create_tray_icon(self) -> Image.Image:
        """
        Create a system tray icon.
        
        Returns:
            Image.Image: PIL Image object for the tray icon
        """
        # Create a simple icon (16x16 pixels)
        width = 16
        height = 16
        
        # Create image with transparent background
        image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # Draw a simple computer monitor icon
        # Monitor outline
        draw.rectangle([2, 3, 13, 10], outline=(255, 255, 255, 255), width=1)
        # Monitor screen
        draw.rectangle([3, 4, 12, 9], fill=(100, 150, 255, 255))
        # Monitor stand
        draw.rectangle([7, 10, 8, 12], fill=(255, 255, 255, 255))
        draw.rectangle([5, 12, 10, 13], fill=(255, 255, 255, 255))
        
        return image
    
    def setup_system_tray(self):
        """Set up the system tray icon and menu."""
        try:
            icon_image = self.create_tray_icon()
            
            # Create menu items
            menu_items = [
                item('Open in Browser', self.open_widget),
                item('Refresh Data', self.refresh_data),
                pystray.Menu.SEPARATOR,
            ]

            # Add floating widget options if available
            if self.use_floating_widget:
                menu_items.extend([
                    item('Show Floating Widget', self.show_floating_widget),
                    item('Hide Floating Widget', self.hide_floating_widget),
                    item('Toggle Floating Widget', self.toggle_floating_widget),
                    pystray.Menu.SEPARATOR,
                ])

            menu_items.extend([
                item('About', self.show_about),
                item('Exit', self.quit_application)
            ])

            menu = pystray.Menu(*menu_items)
            
            # Create tray icon
            self.tray_icon = pystray.Icon(
                "RDP Dashboard",
                icon_image,
                "RDP Dashboard - System Info Widget",
                menu
            )
            
            self.logger.info("System tray icon created successfully")
            
        except Exception as e:
            self.logger.error(f"Error setting up system tray: {e}")
    
    def open_widget(self, icon=None, item=None):
        """
        Open the widget in the default browser.

        Args:
            icon: System tray icon (unused)
            item: Menu item (unused)
        """
        _ = icon, item  # Mark as intentionally unused
        try:
            webbrowser.open(self.widget_url)
            self.logger.info(f"Opened widget URL: {self.widget_url}")
        except Exception as e:
            self.logger.error(f"Error opening widget: {e}")

    def refresh_data(self, icon=None, item=None):
        """
        Refresh the system data.

        Args:
            icon: System tray icon (unused)
            item: Menu item (unused)
        """
        _ = icon, item  # Mark as intentionally unused
        try:
            if self.app_instance and hasattr(self.app_instance, 'refresh_data'):
                self.app_instance.refresh_data()
                self.logger.info("Data refresh requested")
            else:
                self.logger.warning("Cannot refresh data - no app instance available")
        except Exception as e:
            self.logger.error(f"Error refreshing data: {e}")

    def show_floating_widget(self, icon=None, item=None):
        """Show the floating widget."""
        _ = icon, item  # Mark as intentionally unused
        try:
            if self.use_floating_widget and self.floating_widget:
                self.floating_widget.show_widget()
                self.logger.info("Floating widget show requested")
            else:
                self.logger.warning("Floating widget not available")
        except Exception as e:
            self.logger.error(f"Error showing floating widget: {e}")

    def hide_floating_widget(self, icon=None, item=None):
        """Hide the floating widget."""
        _ = icon, item  # Mark as intentionally unused
        try:
            if self.use_floating_widget and self.floating_widget:
                self.floating_widget.hide_widget()
                self.logger.info("Floating widget hide requested")
            else:
                self.logger.warning("Floating widget not available")
        except Exception as e:
            self.logger.error(f"Error hiding floating widget: {e}")

    def toggle_floating_widget(self, icon=None, item=None):
        """Toggle the floating widget visibility."""
        _ = icon, item  # Mark as intentionally unused
        try:
            if self.use_floating_widget and self.floating_widget:
                self.floating_widget.toggle_widget()
                self.logger.info("Floating widget toggle requested")
            else:
                self.logger.warning("Floating widget not available")
        except Exception as e:
            self.logger.error(f"Error toggling floating widget: {e}")

    def show_about(self, icon=None, item=None):
        """
        Show about information.

        Args:
            icon: System tray icon (unused)
            item: Menu item (unused)
        """
        _ = icon, item  # Mark as intentionally unused
        about_text = """
        RDP Dashboard - Desktop Widget

        A lightweight system information widget for Windows desktop.

        Features:
        - Network interface monitoring
        - System performance metrics
        - Computer and user information
        - Real-time updates
        - Floating widget support

        Version: 1.0.0
        """

        # For now, just log the about info
        # In a full implementation, you might show a message box
        self.logger.info("About dialog requested")
        print(about_text)

    def quit_application(self, icon=None, item=None):
        """
        Quit the application.

        Args:
            icon: System tray icon (unused)
            item: Menu item (unused)
        """
        _ = icon, item  # Mark as intentionally unused
        self.logger.info("Application quit requested")
        self.is_running = False

        # Close floating widget if running
        if self.floating_widget:
            self.floating_widget.close_widget()

        if self.tray_icon:
            self.tray_icon.stop()

        if self.app_instance and hasattr(self.app_instance, 'shutdown'):
            self.app_instance.shutdown()

        # Force exit if needed
        sys.exit(0)
    
    def run_tray(self):
        """Run the system tray in a separate thread."""
        if self.tray_icon:
            self.is_running = True
            self.logger.info("Starting system tray")
            self.tray_icon.run()
    
    def initialize_floating_widget(self):
        """Initialize the floating widget if available."""
        try:
            if self.use_floating_widget and HAS_FLOATING_WIDGET:
                from .floating_widget import FloatingWidget
                self.floating_widget = FloatingWidget(
                    app_instance=self.app_instance,
                    widget_url=self.widget_url
                )
                self.logger.info("Floating widget initialized")
            else:
                self.logger.info("Floating widget not available - using browser mode")
        except Exception as e:
            self.logger.error(f"Error initializing floating widget: {e}")
            self.use_floating_widget = False

    def start_floating_widget(self):
        """Start the floating widget if available."""
        try:
            if self.use_floating_widget and self.floating_widget:
                self.floating_widget.start_floating_widget()
                self.logger.info("Floating widget started")
            else:
                self.logger.info("Floating widget not available - opening in browser")
                webbrowser.open(self.widget_url)
        except Exception as e:
            self.logger.error(f"Error starting floating widget: {e}")
            # Fallback to browser
            webbrowser.open(self.widget_url)

    def start_system_tray(self):
        """Start the system tray in a background thread."""
        try:
            self.setup_system_tray()

            # Run tray in separate thread to avoid blocking
            tray_thread = threading.Thread(target=self.run_tray, daemon=True)
            tray_thread.start()

            self.logger.info("System tray started in background thread")

        except Exception as e:
            self.logger.error(f"Error starting system tray: {e}")
    
    def get_screen_dimensions(self) -> Tuple[int, int]:
        """
        Get screen dimensions for window positioning.
        
        Returns:
            Tuple[int, int]: Screen width and height
        """
        try:
            # Try to get screen dimensions using tkinter
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # Hide the window
            
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            
            root.destroy()
            
            self.logger.info(f"Screen dimensions: {screen_width}x{screen_height}")
            return screen_width, screen_height
            
        except Exception as e:
            self.logger.error(f"Error getting screen dimensions: {e}")
            # Return default dimensions if unable to detect
            return 1920, 1080
    
    def calculate_widget_position(self, widget_width: int = 300,
                                widget_height: int = 600) -> Tuple[int, int]:
        """
        Calculate the position for the right-side desktop widget.

        Args:
            widget_width: Width of the widget window
            widget_height: Height of the widget window (for future use)

        Returns:
            Tuple[int, int]: X and Y coordinates for widget positioning
        """
        screen_width, screen_height = self.get_screen_dimensions()
        _ = widget_height, screen_height  # Mark as intentionally unused for now

        # Position on the right side with some margin
        margin = 20
        x_position = screen_width - widget_width - margin
        y_position = margin

        self.logger.info(f"Widget position calculated: ({x_position}, {y_position})")
        return x_position, y_position
    
    def stop(self):
        """Stop the window manager and clean up resources."""
        self.logger.info("Stopping WindowManager")
        self.is_running = False
        
        if self.tray_icon:
            self.tray_icon.stop()
