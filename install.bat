@echo off
echo ========================================
echo RDP Dashboard - Desktop Widget Installer
echo ========================================
echo.

REM Check if conda is installed
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Conda is not installed or not in PATH
    echo Please install Anaconda or Miniconda first
    echo Download from: https://www.anaconda.com/products/distribution
    pause
    exit /b 1
)

echo Creating conda environment with Python 3.12...
conda create -n rdp-dashboard python=3.12 -y

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create conda environment
    pause
    exit /b 1
)

echo.
echo Activating environment and installing dependencies...
call conda activate rdp-dashboard

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to activate conda environment
    pause
    exit /b 1
)

echo Installing Python packages...
pip install -r requirements.txt

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo WARNING: Some packages failed to install.
    echo Trying minimal installation without netifaces...
    echo.
    pip install -r requirements-minimal.txt

    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Failed to install even minimal packages
        pause
        exit /b 1
    ) else (
        echo.
        echo SUCCESS: Minimal installation completed.
        echo Note: Using psutil for network information instead of netifaces.
    )
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To run the application:
echo 1. conda activate rdp-dashboard
echo 2. python main.py
echo.
echo Or simply run: run.bat
echo.
pause
