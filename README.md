# RDP Dashboard - Desktop Widget

A lightweight Windows desktop widget that displays real-time system information including network interfaces, computer details, operating system info, user information, and system performance metrics.

## Features

- **Network Interface Monitoring**: Display all network interfaces with IP addresses (IPv4 and IPv6)
- **Computer Information**: Show computer name, domain, architecture, and boot time
- **Operating System Details**: Display OS version, platform information, and Python version
- **User Information**: Show current logged-in user and session details
- **System Performance**: Real-time CPU, memory, and disk usage monitoring
- **Floating Widget**: True floating desktop widget that can be dragged around and stays on top
- **System Tray Integration**: Minimize to system tray with context menu options
- **Auto-refresh**: Automatic data updates every 5 seconds
- **Modern UI**: Clean, translucent design with glass-like effects
- **Lightweight**: Built with Plotly Dash for minimal resource usage

## Screenshots

The widget appears as a translucent overlay on the right side of your desktop, showing:
- Network interfaces with IP addresses
- Computer name and system information
- Operating system details
- Current user information
- Real-time performance metrics

## Requirements

- Windows 10/11
- Python 3.12
- <PERSON><PERSON>da or <PERSON>conda (recommended)

## Installation

### Automated Installation (Recommended)

1. **Clone or download** this repository to your desired location
2. **Run the installer**: Double-click `install.bat`
   - This will create a conda environment with Python 3.12
   - Install all required dependencies automatically
   - Set up the application for running
3. **Install floating widget support** (optional): Run `install_floating.bat`
   - Adds pywebview for native floating widget functionality
   - If skipped, the app will open in browser mode

### Manual Installation

1. **Create conda environment**:
   ```bash
   conda create -n rdp-dashboard python=3.12
   conda activate rdp-dashboard
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Running the Application

### Quick Start
- Double-click `run.bat` to start the application

### Manual Start
1. **Activate the environment**:
   ```bash
   conda activate rdp-dashboard
   ```

2. **Run the application**:
   ```bash
   python main.py
   ```

## Usage

1. **Starting the Application**:
   - Run `run.bat` or execute `python main.py`
   - The widget will appear on the right side of your screen
   - A system tray icon will appear in your notification area

2. **System Tray Options**:
   - **Open Widget**: Opens the widget in your default browser
   - **Refresh Data**: Manually refresh system information
   - **About**: Display application information
   - **Exit**: Close the application

3. **Widget Features**:
   - Auto-refreshes every 5 seconds
   - Displays real-time system information
   - Translucent design that doesn't interfere with other applications
   - Positioned on the right side of the screen

## Project Structure

```
RDP Dashboard/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── install.bat            # Automated installation script
├── run.bat               # Quick start script
├── README.md             # This file
├── logic/                # Business logic modules
│   ├── system_info.py    # System information collection
│   └── window_manager.py # Window and tray management
├── ui/                   # User interface components
│   └── widget_ui.py      # Dash UI components and layout
├── assets/               # Static assets
│   └── style.css         # Custom CSS styling
├── tests/                # Unit tests
│   └── test_system_info.py # System info tests
└── logs/                 # Application logs (created at runtime)
    └── rdp_dashboard.log # Main log file
```

## Configuration

The application uses the following default settings:
- **Port**: 8050 (localhost)
- **Refresh Rate**: 5 seconds
- **Widget Position**: Right side of screen with 20px margin
- **Log Level**: INFO

To modify these settings, edit the relevant configuration in `main.py` or the respective module files.

## Testing

Run the unit tests to verify the application functionality:

```bash
# Activate environment
conda activate rdp-dashboard

# Run tests
python -m pytest tests/ -v

# Or run specific test file
python tests/test_system_info.py
```

## Troubleshooting

For detailed troubleshooting information, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

### Quick Fixes

1. **Installation Issues**: Try `pip install -r requirements-minimal.txt`
2. **Test Installation**: Run `python test_installation.py`
3. **Test System Info**: Run `python demo.py`
4. **Missing netifaces**: The app automatically uses psutil as fallback

### Common Issues

1. **Application won't start**:
   - Run: `python test_installation.py` to check dependencies
   - Ensure conda environment is activated: `conda activate rdp-dashboard`
   - Try minimal installation: `pip install -r requirements-minimal.txt`

2. **Widget not displaying**:
   - Check if the application is running in the system tray
   - Try opening manually: http://127.0.0.1:8050
   - Check firewall settings for port 8050

3. **netifaces installation fails**:
   - This is expected on some systems
   - The app will automatically use psutil for network information
   - Use `requirements-minimal.txt` for installation without netifaces

### Log Files

Check the log files for detailed error information:
- Location: `logs/rdp_dashboard.log`
- Contains startup, error, and operational information
- Use `LOG_LEVEL = "DEBUG"` in `config.py` for detailed logging

## Development

### Code Style
- Follows PEP8 style guidelines
- Uses type hints where appropriate
- Comprehensive docstrings for all functions and classes
- Modular design with separation of concerns

### Adding Features
1. **System Information**: Extend `logic/system_info.py`
2. **UI Components**: Modify `ui/widget_ui.py`
3. **Window Management**: Update `logic/window_manager.py`
4. **Styling**: Edit `assets/style.css`

### Testing
- Add unit tests for new functionality
- Follow existing test patterns in `tests/`
- Ensure both unit and integration tests pass

## Dependencies

### Core Dependencies
- `dash`: Web framework for the UI
- `dash-bootstrap-components`: UI components
- `psutil`: System information gathering
- `netifaces`: Network interface information
- `pywin32`: Windows-specific features
- `pystray`: System tray integration
- `Pillow`: Image processing for tray icon

### Development Dependencies
- `pytest`: Testing framework
- `black`: Code formatting
- `flake8`: Code linting
- `colorlog`: Colored logging output

## License

This project is provided as-is for educational and personal use. Feel free to modify and distribute according to your needs.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following the code style guidelines
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## Support

For issues, questions, or feature requests:
1. Check the troubleshooting section above
2. Review the log files for error details
3. Create an issue with detailed information about your problem

## Version History

- **v1.0.0**: Initial release
  - Basic system information display
  - Network interface monitoring
  - System tray integration
  - Auto-refresh functionality
  - Modern translucent UI design
