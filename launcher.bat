@echo off
title RDP Dashboard - Launcher
color 0B

:MENU
cls
echo.
echo ========================================
echo    RDP Dashboard - Desktop Widget
echo           Version 1.0.0
echo ========================================
echo.
echo Please select an option:
echo.
echo 1. Install Dependencies
echo 2. Test Installation
echo 3. Run Demo (Test System Info)
echo 4. Start Application
echo 5. Run Tests
echo 6. Open Widget in Browser
echo 7. View README
echo 8. Exit
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto INSTALL
if "%choice%"=="2" goto TESTINSTALL
if "%choice%"=="3" goto DEMO
if "%choice%"=="4" goto RUN
if "%choice%"=="5" goto TEST
if "%choice%"=="6" goto BROWSER
if "%choice%"=="7" goto README
if "%choice%"=="8" goto EXIT

echo Invalid choice. Please try again.
pause
goto MENU

:INSTALL
cls
echo ========================================
echo Installing Dependencies...
echo ========================================
call install.bat
pause
goto MENU

:TESTINSTALL
cls
echo ========================================
echo Testing Installation...
echo ========================================
call conda activate rdp-dashboard
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Environment not found. Please install first.
    pause
    goto MENU
)
python test_installation.py
pause
goto MENU

:DEMO
cls
echo ========================================
echo Running System Information Demo...
echo ========================================
call conda activate rdp-dashboard
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Environment not found. Please install first.
    pause
    goto MENU
)
python demo.py
pause
goto MENU

:RUN
cls
echo ========================================
echo Starting RDP Dashboard Application...
echo ========================================
echo.
echo The application will start in the background.
echo Look for the system tray icon.
echo.
call run.bat
goto MENU

:TEST
cls
echo ========================================
echo Running Unit Tests...
echo ========================================
call run_tests.bat
pause
goto MENU

:BROWSER
cls
echo ========================================
echo Opening Widget in Browser...
echo ========================================
echo.
echo Make sure the application is running first!
echo.
start http://127.0.0.1:8050
echo Widget opened in default browser.
pause
goto MENU

:README
cls
echo ========================================
echo Opening README File...
echo ========================================
start README.md
pause
goto MENU

:EXIT
cls
echo.
echo Thank you for using RDP Dashboard!
echo.
exit /b 0
